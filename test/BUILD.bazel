_TESTS = [
    "test",
    "test_flags",
]

_MSVC_FLAGS = ["/std:c++17", "/permissive-"]
_COPTS = select({
    "//conditions:default": ["-std=c++17"],
    "@rules_cc//cc/compiler:msvc-cl": _MSVC_FLAGS,
    "@rules_cc//cc/compiler:clang-cl": _MSVC_FLAGS, 
})
[cc_test(
    name = test,
    srcs = ["{}.cpp".format(test)],
    deps = ["@magic_enum", ":catch2"],
    copts = _COPTS,
) for test in _TESTS]

# bazel central registry has a catch2 module, but is newer than the one included
# in this repository
cc_library(
    name = "catch2",
    includes = ["3rdparty/Catch2/include"],
    hdrs = ["3rdparty/Catch2/include/catch2/catch.hpp"],
)
