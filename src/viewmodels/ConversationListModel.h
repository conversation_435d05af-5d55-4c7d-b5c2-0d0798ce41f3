#pragma once

#include <QAbstractListModel>

#include <services/conversationmanager.h>

namespace ChamberUI::ViewModels {

class ConversationListModel : public QAbstractListModel {
  Q_OBJECT

public:
  enum Roles {
    ID = Qt::UserRole + 1,
    TITLE,
    TIMESTAMP,
  };

  explicit ConversationListModel(
      Services::ConversationManager &conversation_manager,
      QObject *parent = nullptr);

  int rowCount(const QModelIndex &parent) const override;

  QVariant data(const QModelIndex &index, int role) const override;

private:
  Services::ConversationManager &conversation_manager_;
};

} // namespace ChamberUI::ViewModels