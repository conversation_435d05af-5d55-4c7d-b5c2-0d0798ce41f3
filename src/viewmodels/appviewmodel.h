#ifndef APPVIEWMODEL_H
#define APPVIEWMODEL_H

#include "services/database_service.h"
#include <QObject>
#include <QString>
#include <qqmlintegration.h>

class AppViewModel : public QObject {
    Q_OBJECT
    QML_ELEMENT
    Q_PROPERTY(bool darkMode READ darkMode WRITE setDarkMode NOTIFY darkModeChanged)
    Q_PROPERTY(QString appVersion READ appVersion CONSTANT)

public:
    explicit AppViewModel(QObject *parent = nullptr);

    bool darkMode() const;

    void setDarkMode(bool darkMode);

    QString appVersion() const;

signals:
    void darkModeChanged();

public slots:
    // Settings methods
    void saveSettings();

    void loadSettings();

private:
    bool m_darkMode;
    QString m_appVersion;
    DatabaseService *m_databaseService;
};

#endif // APPVIEWMODEL_H
