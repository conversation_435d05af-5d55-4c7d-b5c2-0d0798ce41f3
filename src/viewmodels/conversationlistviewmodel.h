#ifndef CONVERSATIONLISTVIEWMODEL_H
#define CONVERSATIONLISTVIEWMODEL_H

#include <qqmlintegration.h>
#include "services/conversationmanager.h"
#include "services/exportservice.h"
#include "services/importservice.h"

class ConversationListViewModel : public QObject {
    Q_OBJECT
    QML_ELEMENT
    Q_PROPERTY(QVariantList conversations READ conversations NOTIFY conversationsChanged)
    Q_PROPERTY(
        QString selectedConversationId READ selectedConversationId WRITE setSelectedConversationId NOTIFY
        selectedConversationChanged)

public:
    explicit ConversationListViewModel(QObject *parent = nullptr);

    QVariantList conversations() const;

    QString selectedConversationId() const;

    void setSelectedConversationId(const QString &id);

public slots:
    void createNewConversation();

    void deleteConversation(const QString &id);

    void renameConversation(const QString &id, const QString &newTitle);

    void refreshConversations();

    void selectConversation(const QString &id);

    void exportConversation(const QString &id, const QString &format);

    void importConversation();

    void showImportDialog();
    void importConversation(const QString &filePath);
    void handleExportFileSelected(const QString &id, const QString &filePath, const QString &format);

signals:
    void conversationsChanged();

    void selectedConversationChanged();

    void conversationSelected(const QString &id);

    void conversationCreated(const QString &id);

    void conversationDeleted(const QString &id);

    void conversationRenamed(const QString &id, const QString &newTitle);

    void exportStarted(const QString &id);

    void exportCompleted(const QString &id, const QString &filePath);

    void exportFailed(const QString &id, const QString &error);

    void importStarted();

    void importCompleted(const QString &id);

    void importFailed(const QString &error);

    void requestImportFileDialog();
    void requestExportFileDialog(const QString &id, const QString &suggestedPath, const QString &filter);

private:
    QVariantList m_conversations;
    QString m_selectedConversationId;
    ConversationManager *m_conversationManager;
    ExportService *m_exportService;
    ImportService *m_importService;

    void loadConversations();

    void connectSignals();
};

#endif // CONVERSATIONLISTVIEWMODEL_H
