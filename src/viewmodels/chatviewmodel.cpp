#include "chatviewmodel.h"
#include "../services/messageprocessor.h"
#include "../services/conversationmanager.h"
#include <QDateTime>
#include <QTimer>
#include <QUuid>

ChatViewModel::ChatViewModel(QObject *parent)
    : QObject(parent)
    , m_isProcessing(false)
    , m_messageProcessor(new MessageProcessor(this))
    , m_conversationManager(new ConversationManager(this))
{
    // Connect message processor signals
    connect(m_messageProcessor, &MessageProcessor::responseReceived,
            this, &ChatViewModel::handleResponseReceived);
    connect(m_messageProcessor, &MessageProcessor::errorOccurred,
            this, &ChatViewModel::handleErrorOccurred);
    connect(m_messageProcessor, &MessageProcessor::processingStarted,
            this, &ChatViewModel::handleProcessingStarted);
    connect(m_messageProcessor, &MessageProcessor::processingFinished,
            this, &ChatViewModel::handleProcessingFinished);
}

QVariantList ChatViewModel::messages() const
{
    return m_messages;
}

QString ChatViewModel::inputText() const
{
    return m_inputText;
}

void ChatViewModel::setInputText(const QString &text)
{
    if (m_inputText != text) {
        m_inputText = text;
        emit inputTextChanged();
    }
}

bool ChatViewModel::isProcessing() const
{
    return m_isProcessing;
}

QString ChatViewModel::currentConversationId() const
{
    return m_currentConversationId;
}

QVariantMap ChatViewModel::currentConversation() const
{
    if (m_currentConversationId.isEmpty()) {
        return QVariantMap();
    }
    
    return m_conversationManager->getConversation(m_currentConversationId);
}

QVariantList ChatViewModel::conversationTemplates() const
{
    QVariantList allConversations = m_conversationManager->getAllConversations();
    QVariantList templates;
    
    for (const QVariant &conversationVar : allConversations) {
        QVariantMap conversation = conversationVar.toMap();
        if (conversation["isTemplate"].toBool()) {
            templates.append(conversation);
        }
    }
    
    return templates;
}

void ChatViewModel::sendMessage()
{
    if (m_inputText.trimmed().isEmpty() || m_isProcessing) {
        return;
    }
    
    // Create user message
    QVariantMap userMessage;
    userMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    userMessage["content"] = m_inputText;
    userMessage["role"] = "user";
    userMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // Add to messages list
    addMessage(userMessage);
    
    // Save message to database
    saveMessage(userMessage);
    
    // Emit signal for message processing
    emit messageSent(m_currentConversationId, m_inputText);
    
    // Clear input text
    setInputText("");
    
    // Get the current conversation settings
    QVariantMap conversationSettings = currentConversation();
    
    // Process the message using the message processor with conversation settings
    m_messageProcessor->processMessage(m_currentConversationId, m_inputText, conversationSettings);
}

void ChatViewModel::sendMessageWithAttachments(const QVariantList &attachments)
{
    if ((m_inputText.trimmed().isEmpty() && attachments.isEmpty()) || m_isProcessing) {
        return;
    }
    
    // Create user message
    QVariantMap userMessage;
    userMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    userMessage["content"] = m_inputText;
    userMessage["role"] = "user";
    userMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // Process attachments
    if (!attachments.isEmpty()) {
        processAttachments(userMessage, attachments);
    }
    
    // Add to messages list
    addMessage(userMessage);
    
    // Save message to database
    saveMessage(userMessage);
    
    // Emit signal for message processing
    emit messageSent(m_currentConversationId, m_inputText);
    
    // Clear input text
    setInputText("");
    
    // Get the current conversation settings
    QVariantMap conversationSettings = currentConversation();
    
    // Process the message using the message processor with conversation settings
    m_messageProcessor->processMessage(m_currentConversationId, m_inputText, conversationSettings);
}

void ChatViewModel::handleResponseReceived(const QString &conversationId, const QString &response)
{
    // Only handle responses for the current conversation
    if (conversationId != m_currentConversationId) {
        return;
    }
    
    // Create AI response message
    QVariantMap aiMessage;
    aiMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    aiMessage["content"] = response;
    aiMessage["role"] = "assistant";
    aiMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // Add to messages list
    addMessage(aiMessage);
    
    // Save message to database
    saveMessage(aiMessage);
}

void ChatViewModel::handleErrorOccurred(const QString &conversationId, const QString &error)
{
    // Only handle errors for the current conversation
    if (conversationId != m_currentConversationId) {
        return;
    }
    
    // Create error message
    QVariantMap errorMessage;
    errorMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    errorMessage["content"] = "Error: " + error;
    errorMessage["role"] = "system";
    errorMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // Add to messages list
    addMessage(errorMessage);
}

void ChatViewModel::handleProcessingStarted(const QString &conversationId)
{
    // Only handle events for the current conversation
    if (conversationId != m_currentConversationId) {
        return;
    }
    
    m_isProcessing = true;
    emit isProcessingChanged();
}

void ChatViewModel::handleProcessingFinished(const QString &conversationId)
{
    // Only handle events for the current conversation
    if (conversationId != m_currentConversationId) {
        return;
    }
    
    m_isProcessing = false;
    emit isProcessingChanged();
}

void ChatViewModel::loadConversation(const QString &conversationId)
{
    if (m_currentConversationId == conversationId) {
        return;
    }
    
    m_currentConversationId = conversationId;
    
    // Clear current messages
    m_messages.clear();
    
    // Load messages from the database
    QVariantList messages = m_conversationManager->getConversationMessages(conversationId);
    
    if (messages.isEmpty()) {
        // If no messages, add a welcome message
        QVariantMap welcomeMessage;
        welcomeMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
        welcomeMessage["content"] = "This is the beginning of your conversation.";
        welcomeMessage["role"] = "system";
        welcomeMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
        
        addMessage(welcomeMessage);
        saveMessage(welcomeMessage);
    } else {
        // Add all messages to the view
        for (const QVariant &messageVar : messages) {
            m_messages.append(messageVar);
        }
        emit messagesChanged();
    }
    
    emit currentConversationChanged();
}

void ChatViewModel::saveConversationSettings(const QVariantMap &conversation)
{
    if (conversation.isEmpty() || !conversation.contains("id")) {
        return;
    }
    
    // Save the conversation to the database
    // Use saveConversations() instead of saveConversation() which doesn't exist
    m_conversationManager->saveConversations();
    
    // If this is the current conversation, update the provider and model
    if (conversation["id"].toString() == m_currentConversationId) {
        if (conversation.contains("provider")) {
            m_messageProcessor->setProvider(conversation["provider"].toString());
        }
        
        // Update provider settings if needed
        QVariantMap providerSettings;
        if (conversation.contains("model")) {
            providerSettings["model"] = conversation["model"];
        }
        
        if (!providerSettings.isEmpty()) {
            m_messageProcessor->setProviderSettings(providerSettings);
        }
    }
    
    emit conversationSettingsSaved();
}

void ChatViewModel::clearChat()
{
    if (m_currentConversationId.isEmpty()) {
        return;
    }
    
    // Clear messages in the database
    m_conversationManager->clearConversation(m_currentConversationId);
    
    // Clear messages in the view
    m_messages.clear();
    emit messagesChanged();
    
    // Add a system message indicating the chat was cleared
    QVariantMap clearMessage;
    clearMessage["id"] = QUuid::createUuid().toString(QUuid::WithoutBraces);
    clearMessage["content"] = "Chat history has been cleared.";
    clearMessage["role"] = "system";
    clearMessage["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    addMessage(clearMessage);
    saveMessage(clearMessage);
}

void ChatViewModel::deleteMessage(const QString &messageId)
{
    // Find the message in the list
    for (int i = 0; i < m_messages.size(); ++i) {
        QVariantMap message = m_messages[i].toMap();
        if (message["id"].toString() == messageId) {
            // Remove from the list
            m_messages.removeAt(i);
            emit messagesChanged();
            
            // Delete from database
            m_conversationManager->deleteMessage(m_currentConversationId, messageId);
            
            break;
        }
    }
}

void ChatViewModel::addAttachment(const QString &messageId, const QString &filePath, const QString &fileType)
{
    // Find the message in the list
    for (int i = 0; i < m_messages.size(); ++i) {
        QVariantMap message = m_messages[i].toMap();
        if (message["id"].toString() == messageId) {
            // Add attachment to the message
            QString attachmentId = m_conversationManager->addAttachment(
                m_currentConversationId,
                messageId,
                filePath,
                fileType == "image" ? Attachment::Image :
                fileType == "pdf" ? Attachment::PDF :
                fileType == "text" ? Attachment::Text :
                Attachment::Other
            );
            
            if (!attachmentId.isEmpty()) {
                // Reload the message to get updated attachments
                QVariantMap updatedMessage = m_conversationManager->getConversation(m_currentConversationId)
                    .value("messages").toList()
                    .at(i).toMap();
                
                m_messages[i] = updatedMessage;
                emit messagesChanged();
                emit attachmentAdded(messageId, attachmentId);
            }
            
            break;
        }
    }
}

void ChatViewModel::removeAttachment(const QString &messageId, const QString &attachmentId)
{
    // Find the message in the list
    for (int i = 0; i < m_messages.size(); ++i) {
        QVariantMap message = m_messages[i].toMap();
        if (message["id"].toString() == messageId) {
            // Remove attachment from the message
            bool success = m_conversationManager->removeAttachment(
                m_currentConversationId,
                messageId,
                attachmentId
            );
            
            if (success) {
                // Reload the message to get updated attachments
                QVariantMap updatedMessage = m_conversationManager->getConversation(m_currentConversationId)
                    .value("messages").toList()
                    .at(i).toMap();
                
                m_messages[i] = updatedMessage;
                emit messagesChanged();
                emit attachmentRemoved(messageId, attachmentId);
            }
            
            break;
        }
    }
}

void ChatViewModel::processAttachments(QVariantMap &message, const QVariantList &attachments)
{
    if (attachments.isEmpty()) {
        return;
    }
    
    QVariantList processedAttachments;
    
    for (const QVariant &attachmentVar : attachments) {
        QVariantMap attachment = attachmentVar.toMap();
        
        // Get file info
        QString filePath = attachment["path"].toString();
        QString fileName = attachment["filename"].toString();
        QString fileType = attachment["type"].toString();
        
        // Determine attachment type
        Attachment::Type type = Attachment::Other;
        if (fileType == "image") {
            type = Attachment::Image;
        } else if (fileType == "pdf") {
            type = Attachment::PDF;
        } else if (fileType == "text") {
            type = Attachment::Text;
        }
        
        // Save the attachment file to the storage location
        QString savedPath = m_conversationManager->saveAttachmentFile(filePath, fileName);
        if (!savedPath.isEmpty()) {
            // Create attachment object
            Attachment newAttachment;
            newAttachment.setFilename(fileName);
            newAttachment.setPath(savedPath);
            newAttachment.setType(type);
            
            // Add to processed attachments
            processedAttachments.append(newAttachment.toVariantMap());
        }
    }
    
    if (!processedAttachments.isEmpty()) {
        message["attachments"] = processedAttachments;
    }
}

void ChatViewModel::addMessage(const QVariantMap &message)
{
    m_messages.append(message);
    emit messagesChanged();
}

void ChatViewModel::saveMessage(const QVariantMap &message)
{
    if (m_currentConversationId.isEmpty()) {
        return;
    }
    
    // Save message to the database using conversation manager
    m_conversationManager->addMessage(
        m_currentConversationId,
        message["content"].toString(),
        message["role"].toString()
    );
}

void ChatViewModel::createNewConversation(const QString &title, const QString &provider, const QString &model,
                                        const QString &systemPrompt, double temperature, int maxTokens)
{
    // Create a new conversation
    QString conversationId = m_conversationManager->createConversation(title);
    
    // Set conversation settings
    QVariantMap conversation = m_conversationManager->getConversation(conversationId);
    conversation["provider"] = provider;
    conversation["model"] = model;
    conversation["systemPrompt"] = systemPrompt;
    conversation["temperature"] = temperature;
    conversation["maxTokens"] = maxTokens;
    // Save the conversation
    // Use saveConversations() instead of saveConversation() which doesn't exist
    m_conversationManager->saveConversations();
    
    // Load the new conversation
    loadConversation(conversationId);
    
    // Emit signal
    emit conversationCreated(conversationId);
}

void ChatViewModel::createConversationFromTemplate(const QString &templateId)
{
    // Get the template
    QVariantMap templateConversation = m_conversationManager->getConversation(templateId);
    if (templateConversation.isEmpty() || !templateConversation["isTemplate"].toBool()) {
        return;
    }
    
    // Create a new conversation with a title based on the template
    QString title = templateConversation["templateName"].toString();
    if (title.isEmpty()) {
        title = "New Conversation";
    }
    
    // Create the conversation
    QString conversationId = m_conversationManager->createConversation(title);
    
    // Copy settings from template
    QVariantMap newConversation = m_conversationManager->getConversation(conversationId);
    newConversation["provider"] = templateConversation["provider"];
    newConversation["model"] = templateConversation["model"];
    newConversation["systemPrompt"] = templateConversation["systemPrompt"];
    newConversation["temperature"] = templateConversation["temperature"];
    newConversation["maxTokens"] = templateConversation["maxTokens"];
    
    if (templateConversation.contains("settings")) {
        newConversation["settings"] = templateConversation["settings"];
    }
    
    // Save the conversation
    // Use saveConversations() instead of saveConversation() which doesn't exist
    m_conversationManager->saveConversations();
    
    // Load the new conversation
    loadConversation(conversationId);
    
    // Emit signal
    emit conversationCreated(conversationId);
}

void ChatViewModel::saveAsTemplate(const QString &templateName)
{
    if (m_currentConversationId.isEmpty()) {
        return;
    }
    
    // Get the current conversation
    QVariantMap conversation = m_conversationManager->getConversation(m_currentConversationId);
    if (conversation.isEmpty()) {
        return;
    }
    
    // Create a new conversation for the template
    QString templateId = m_conversationManager->createConversation(templateName);
    
    // Copy settings from current conversation
    QVariantMap templateConversation = m_conversationManager->getConversation(templateId);
    templateConversation["provider"] = conversation["provider"];
    templateConversation["model"] = conversation["model"];
    templateConversation["systemPrompt"] = conversation["systemPrompt"];
    templateConversation["temperature"] = conversation["temperature"];
    templateConversation["maxTokens"] = conversation["maxTokens"];
    
    if (conversation.contains("settings")) {
        templateConversation["settings"] = conversation["settings"];
    }
    
    // Mark as template
    templateConversation["isTemplate"] = true;
    templateConversation["templateName"] = templateName;
    
    // Save the template
    // Use saveConversations() instead of saveConversation() which doesn't exist
    m_conversationManager->saveConversations();
    
    // Emit signal
    emit conversationTemplatesChanged();
}