#ifndef CHATVIEWMODEL_H
#define CHATVIEWMODEL_H

#include <QObject>
#include <QString>
#include <QList>
#include <qqmlintegration.h>
#include <QVariantMap>

#include "../backup/conversation.h"

class MessageProcessor;
class ConversationManager;

class ChatViewModel : public QObject
{
    Q_OBJECT
    QML_ELEMENT
    Q_PROPERTY(QVariantList messages READ messages NOTIFY messagesChanged)
    Q_PROPERTY(QString inputText READ inputText WRITE setInputText NOTIFY inputTextChanged)
    Q_PROPERTY(bool isProcessing READ isProcessing NOTIFY isProcessingChanged)
    Q_PROPERTY(QString currentConversationId READ currentConversationId NOTIFY currentConversationChanged)
    Q_PROPERTY(QVariantMap currentConversation READ currentConversation NOTIFY currentConversationChanged)
    Q_PROPERTY(QVariantList conversationTemplates READ conversationTemplates NOTIFY conversationTemplatesChanged)

public:
    explicit ChatViewModel(QObject *parent = nullptr);

    QVariantList messages() const;
    QString inputText() const;
    void setInputText(const QString &text);
    bool isProcessing() const;
    QString currentConversationId() const;
    Conversation& currentConversation() const;
    QVariantList conversationTemplates() const;

public slots:
    void sendMessage();
    void sendMessageWithAttachments(const QVariantList &attachments);
    void loadConversation(const QString &conversationId);
    void clearChat();
    void deleteMessage(const QString &messageId);
    void addAttachment(const QString &messageId, const QString &filePath, const QString &fileType);
    void removeAttachment(const QString &messageId, const QString &attachmentId);

    // Conversation settings methods
    void saveConversationSettings(const QVariantMap &conversation);
    void createNewConversation(const QString &title, const QString &provider, const QString &model,
                              const QString &systemPrompt, double temperature, int maxTokens);
    void createConversationFromTemplate(const QString &templateId);
    void saveAsTemplate(const QString &templateName);

signals:
    void messagesChanged();
    void inputTextChanged();
    void isProcessingChanged();
    void currentConversationChanged();
    void conversationTemplatesChanged();
    void messageSent(const QString &conversationId, const QString &content);
    void attachmentAdded(const QString &messageId, const QString &attachmentId);
    void attachmentRemoved(const QString &messageId, const QString &attachmentId);
    void conversationSettingsSaved();
    void conversationCreated(const QString &conversationId);

private slots:
    void handleResponseReceived(const QString &conversationId, const QString &response);
    void handleErrorOccurred(const QString &conversationId, const QString &error);
    void handleProcessingStarted(const QString &conversationId);
    void handleProcessingFinished(const QString &conversationId);

private:
    QVariantList m_messages;
    QString m_inputText;
    bool m_isProcessing;
    QString m_currentConversationId;
    MessageProcessor *m_messageProcessor;
    ConversationManager *m_conversationManager;

    void addMessage(const QVariantMap &message);
    void saveMessage(const QVariantMap &message);
    void processAttachments(QVariantMap &message, const QVariantList &attachments);
};

#endif // CHATVIEWMODEL_H