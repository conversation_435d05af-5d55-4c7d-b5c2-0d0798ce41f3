#include "settingsviewmodel.h"
#include "../services/database_service.h"
#include "../services/messageprocessor.h"
#include "../services/providermanager.h"
#include "../services/voiceservice.h"
#include <QDebug>

SettingsViewModel::SettingsViewModel(QObject *parent)
    : QObject(parent)
    , m_selectedProvider("openai")
    , m_saveHistory(true)
    , m_autoDeleteHistory(false)
    , m_historyRetentionDays(30)
    , m_voiceSettings()
    , m_providerManager(new ProviderManager(this))
    , m_messageProcessor(new MessageProcessor(this))
    , m_databaseService(DatabaseService::instance(this))
{
    // Initialize database
    if (!m_databaseService->initialize()) {
        qWarning() << "Failed to initialize database service";
    }

    // Get available providers from the provider manager
    m_availableProviders = m_providerManager->availableProviders();

    // Initialize default provider settings
    m_providerSettings["apiKey"] = "";
    m_providerSettings["model"] = "gpt-4o";
    m_providerSettings["temperature"] = 0.7;
    m_providerSettings["maxTokens"] = 2000;

    // Load saved settings
    loadSettings();

    // Connect provider manager signals
    connect(m_providerManager, &ProviderManager::providerRegistered,
            this, &SettingsViewModel::updateAvailableProviders);
    connect(m_providerManager, &ProviderManager::currentProviderChanged,
            this, &SettingsViewModel::handleCurrentProviderChanged);
}

QString SettingsViewModel::selectedProvider() const
{
    return m_selectedProvider;
}

void SettingsViewModel::setSelectedProvider(const QString &provider)
{
    if (m_selectedProvider != provider) {
        m_selectedProvider = provider;

        // Update the provider in the provider manager
        m_providerManager->setCurrentProvider(provider);

        // Update the provider in the message processor
        m_messageProcessor->setProvider(provider);

        // Load provider-specific settings
        loadSettings();

        emit selectedProviderChanged();
    }
}

QVariantList SettingsViewModel::availableProviders() const
{
    return m_availableProviders;
}

QVariantMap SettingsViewModel::providerSettings() const
{
    return m_providerSettings;
}

void SettingsViewModel::setProviderSettings(const QVariantMap &settings)
{
    m_providerSettings = settings;

    // Update the settings in the provider manager
    m_providerManager->setProviderSettings(m_selectedProvider, settings);

    // Update the settings in the message processor
    m_messageProcessor->setProviderSettings(settings);

    emit providerSettingsChanged();
}

bool SettingsViewModel::saveHistory() const
{
    return m_saveHistory;
}

void SettingsViewModel::setSaveHistory(bool save)
{
    if (m_saveHistory != save) {
        m_saveHistory = save;
        emit saveHistoryChanged();
    }
}

bool SettingsViewModel::autoDeleteHistory() const
{
    return m_autoDeleteHistory;
}

void SettingsViewModel::setAutoDeleteHistory(bool autoDelete)
{
    if (m_autoDeleteHistory != autoDelete) {
        m_autoDeleteHistory = autoDelete;
        emit autoDeleteHistoryChanged();
    }
}

int SettingsViewModel::historyRetentionDays() const
{
    return m_historyRetentionDays;
}

void SettingsViewModel::setHistoryRetentionDays(int days)
{
    if (m_historyRetentionDays != days) {
        m_historyRetentionDays = days;
        emit historyRetentionDaysChanged();
    }
}

QVariantMap SettingsViewModel::voiceSettings() const
{
    return m_voiceSettings;
}

void SettingsViewModel::setVoiceSettings(const QVariantMap &settings)
{
    if (m_voiceSettings != settings) {
        m_voiceSettings = settings;
        emit voiceSettingsChanged();
    }
}

void SettingsViewModel::saveSettings()
{
    // Create settings map
    QVariantMap allSettings;

    // Save provider settings
    allSettings["selectedProvider"] = m_selectedProvider;

    // Save provider-specific settings
    QMapIterator<QString, QVariant> i(m_providerSettings);
    QVariantMap providerSpecificSettings;
    while (i.hasNext()) {
        i.next();
        providerSpecificSettings[i.key()] = i.value();
    }
    allSettings["provider_" + m_selectedProvider] = providerSpecificSettings;

    // Save history settings
    QVariantMap historySettings;
    historySettings["saveHistory"] = m_saveHistory;
    historySettings["autoDeleteHistory"] = m_autoDeleteHistory;
    historySettings["retentionDays"] = m_historyRetentionDays;
    allSettings["history"] = historySettings;

    // Save voice settings
    allSettings["voice"] = m_voiceSettings;

    // Save to database
    if (!m_databaseService->saveSettings(allSettings)) {
        qWarning() << "Failed to save settings to database";
    }

    // Save API key securely if provided
    if (m_providerSettings.contains("apiKey") && !m_providerSettings["apiKey"].toString().isEmpty()) {
        m_databaseService->saveProviderKey(m_selectedProvider, m_providerSettings["apiKey"].toString());
    }

    // Update the provider manager and message processor with the new settings
    m_providerManager->setCurrentProvider(m_selectedProvider);
    m_providerManager->setProviderSettings(m_selectedProvider, m_providerSettings);
    m_messageProcessor->setProvider(m_selectedProvider);
    m_messageProcessor->setProviderSettings(m_providerSettings);

    emit settingsSaved();
}

void SettingsViewModel::loadSettings()
{
    // Load all settings from database
    QVariantMap allSettings = m_databaseService->getSettings();

    // Load provider settings
    if (allSettings.contains("selectedProvider")) {
        m_selectedProvider = allSettings["selectedProvider"].toString();
    }

    // Load provider-specific settings
    if (allSettings.contains("provider_" + m_selectedProvider)) {
        QVariantMap providerSettings = allSettings["provider_" + m_selectedProvider].toMap();
        for (auto it = providerSettings.constBegin(); it != providerSettings.constEnd(); ++it) {
            m_providerSettings[it.key()] = it.value();
        }
    }

    // Load API key from secure storage
    QString apiKey = m_databaseService->getProviderKey(m_selectedProvider);
    if (!apiKey.isEmpty()) {
        m_providerSettings["apiKey"] = apiKey;
    }

    // Load history settings
    if (allSettings.contains("history")) {
        QVariantMap historySettings = allSettings["history"].toMap();
        if (historySettings.contains("saveHistory")) {
            m_saveHistory = historySettings["saveHistory"].toBool();
        }
        if (historySettings.contains("autoDeleteHistory")) {
            m_autoDeleteHistory = historySettings["autoDeleteHistory"].toBool();
        }
        if (historySettings.contains("retentionDays")) {
            m_historyRetentionDays = historySettings["retentionDays"].toInt();
        }
    }

    // Load voice settings
    if (allSettings.contains("voice")) {
        m_voiceSettings = allSettings["voice"].toMap();
    }

    // Update the provider manager and message processor with the loaded settings
    m_providerManager->setCurrentProvider(m_selectedProvider);
    m_providerManager->setProviderSettings(m_selectedProvider, m_providerSettings);
    m_messageProcessor->setProvider(m_selectedProvider);
    m_messageProcessor->setProviderSettings(m_providerSettings);

    // Emit signals
    emit selectedProviderChanged();
    emit providerSettingsChanged();
    emit saveHistoryChanged();
    emit autoDeleteHistoryChanged();
    emit historyRetentionDaysChanged();
    emit voiceSettingsChanged();
}

void SettingsViewModel::resetSettings()
{
    // Reset to default values
    m_selectedProvider = "openai";

    m_providerSettings.clear();
    m_providerSettings["apiKey"] = "";
    m_providerSettings["model"] = "gpt-4o";
    m_providerSettings["temperature"] = 0.7;
    m_providerSettings["maxTokens"] = 2000;

    m_saveHistory = true;
    m_autoDeleteHistory = false;
    m_historyRetentionDays = 30;

    // Reset voice settings
    m_voiceSettings.clear();
    m_voiceSettings["voice"] = "";
    m_voiceSettings["speechRate"] = 1.0;
    m_voiceSettings["pitch"] = 1.0;
    m_voiceSettings["volume"] = 1.0;
    m_voiceSettings["language"] = "en-US";

    // Update the provider manager and message processor with the reset settings
    m_providerManager->setCurrentProvider(m_selectedProvider);
    m_providerManager->setProviderSettings(m_selectedProvider, m_providerSettings);
    m_messageProcessor->setProvider(m_selectedProvider);
    m_messageProcessor->setProviderSettings(m_providerSettings);

    // Emit signals
    emit selectedProviderChanged();
    emit providerSettingsChanged();
    emit saveHistoryChanged();
    emit autoDeleteHistoryChanged();
    emit historyRetentionDaysChanged();
    emit voiceSettingsChanged();
}

void SettingsViewModel::addProvider(const QString &name, const QVariantMap &settings)
{
    // This is now handled by the provider manager
    // We just need to update our local list when the provider manager changes
    updateAvailableProviders();
}

void SettingsViewModel::removeProvider(const QString &name)
{
    // Don't remove built-in providers
    if (name == "openai" || name == "anthropic" || name == "placeholder") {
        return;
    }

    // If the removed provider was selected, select the default provider
    if (m_selectedProvider == name) {
        setSelectedProvider("openai");
    }

    // Update available providers
    updateAvailableProviders();
}

void SettingsViewModel::updateAvailableProviders()
{
    // Get the latest list of providers from the provider manager
    m_availableProviders = m_providerManager->availableProviders();
    emit availableProvidersChanged();
}

void SettingsViewModel::handleCurrentProviderChanged(const QString &name)
{
    if (m_selectedProvider != name) {
        m_selectedProvider = name;

        // Load provider-specific settings
        loadSettings();

        emit selectedProviderChanged();
    }
}