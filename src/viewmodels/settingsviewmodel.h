#ifndef SETTINGSVIEWMODEL_H
#define SETTINGSVIEWMODEL_H

#include <QObject>
#include <QString>
#include <QString>
#include <qqmlintegration.h>
#include <QVariantList>
#include <QVariantMap>

class ProviderManager;
class MessageProcessor;
class DatabaseService;

class SettingsViewModel : public QObject
{
    Q_OBJECT
    QML_ELEMENT
    Q_PROPERTY(QString selectedProvider READ selectedProvider WRITE setSelectedProvider NOTIFY selectedProviderChanged)
    Q_PROPERTY(QVariantList availableProviders READ availableProviders NOTIFY availableProvidersChanged)
    Q_PROPERTY(QVariantMap providerSettings READ providerSettings WRITE setProviderSettings NOTIFY providerSettingsChanged)
    Q_PROPERTY(bool saveHistory READ saveHistory WRITE setSaveHistory NOTIFY saveHistoryChanged)
    Q_PROPERTY(bool autoDeleteHistory READ autoDeleteHistory WRITE setAutoDeleteHistory NOTIFY autoDeleteHistoryChanged)
    Q_PROPERTY(int historyRetentionDays READ historyRetentionDays WRITE setHistoryRetentionDays NOTIFY historyRetentionDaysChanged)
    Q_PROPERTY(QVariantMap voiceSettings READ voiceSettings WRITE setVoiceSettings NOTIFY voiceSettingsChanged)

public:
    explicit SettingsViewModel(QObject *parent = nullptr);

    QString selectedProvider() const;
    void setSelectedProvider(const QString &provider);

    QVariantList availableProviders() const;

    QVariantMap providerSettings() const;
    void setProviderSettings(const QVariantMap &settings);

    bool saveHistory() const;
    void setSaveHistory(bool save);

    bool autoDeleteHistory() const;
    void setAutoDeleteHistory(bool autoDelete);

    int historyRetentionDays() const;
    void setHistoryRetentionDays(int days);

    QVariantMap voiceSettings() const;
    void setVoiceSettings(const QVariantMap &settings);

public slots:
    void saveSettings();
    void loadSettings();
    void resetSettings();
    void addProvider(const QString &name, const QVariantMap &settings);
    void removeProvider(const QString &name);

private slots:
    void updateAvailableProviders();
    void handleCurrentProviderChanged(const QString &name);

signals:
    void selectedProviderChanged();
    void availableProvidersChanged();
    void providerSettingsChanged();
    void saveHistoryChanged();
    void autoDeleteHistoryChanged();
    void historyRetentionDaysChanged();
    void voiceSettingsChanged();
    void settingsSaved();

private:
    QString m_selectedProvider;
    QVariantList m_availableProviders;
    QVariantMap m_providerSettings;
    bool m_saveHistory;
    bool m_autoDeleteHistory;
    int m_historyRetentionDays;
    QVariantMap m_voiceSettings;
    ProviderManager *m_providerManager;
    MessageProcessor *m_messageProcessor;
    DatabaseService *m_databaseService;
};

#endif // SETTINGSVIEWMODEL_H