#include "ConversationListModel.h"

namespace ChamberUI::ViewModels {

ConversationListModel::ConversationListModel(
    Services::ConversationManager &conversation_manager, QObject *parent)
    : QAbstractListModel(parent), conversation_manager_(conversation_manager) {}

int ConversationListModel::rowCount(const QModelIndex &) const {
  return conversation_manager_.getConversationCount();
}

QVariant ConversationListModel::data(const QModelIndex &index, int role) const {
  if (!index.isValid() || index.row() >= conversation_manager_.getConversationCount()) {
    return QVariant();
  }
  switch (role) {
  case ID:
    return conversation_manager_.getConversationId(index.row());
  default:
    return QVariant();
  }
}


} // namespace ChamberUI::ViewModels
