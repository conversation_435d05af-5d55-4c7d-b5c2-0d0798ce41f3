#include "appviewmodel.h"
#include <QGuiApplication>
#include <QDebug>

AppViewModel::AppViewModel(QObject *parent)
    : QObject(parent)
    , m_darkMode(true) // Default to dark mode
    , m_appVersion(QGuiApplication::applicationVersion())
    , m_databaseService(DatabaseService::instance(this))
{
    // Initialize database
    if (!m_databaseService->initialize()) {
        qWarning() << "Failed to initialize database service";
    }

    // Load settings
    loadSettings();
}

bool AppViewModel::darkMode() const
{
    return m_darkMode;
}

void AppViewModel::setDarkMode(bool darkMode)
{
    if (m_darkMode != darkMode) {
        m_darkMode = darkMode;
        emit darkModeChanged();

        // Save setting
        m_databaseService->saveSetting("darkMode", darkMode);
    }
}

QString AppViewModel::appVersion() const
{
    return m_appVersion;
}

void AppViewModel::saveSettings()
{
    QVariantMap settings;
    settings["darkMode"] = m_darkMode;

    if (!m_databaseService->saveSettings(settings)) {
        qWarning() << "Failed to save settings";
    }
}

void AppViewModel::loadSettings()
{
    // Load dark mode setting
    QVariant darkModeSetting = m_databaseService->getSetting("darkMode");
    if (darkModeSetting.isValid()) {
        setDarkMode(darkModeSetting.toBool());
    }
}