#include "conversationlistviewmodel.h"
#include <QDateTime>
#include <QUuid>
#include <QDebug>
#include <algorithm>

ConversationListViewModel::ConversationListViewModel(QObject *parent)
    : QObject(parent)
    , m_conversationManager(new ConversationManager(this))
    , m_exportService(new ExportService(this))
    , m_importService(new ImportService(this))
{
    // Connect signals from conversation manager
    connectSignals();
    
    // Load conversations from database
    loadConversations();
    
    // If no conversations exist, create a default one
    if (m_conversations.isEmpty()) {
        createNewConversation();
    }
}

QVariantList ConversationListViewModel::conversations() const
{
    return m_conversations;
}

QString ConversationListViewModel::selectedConversationId() const
{
    return m_selectedConversationId;
}

void ConversationListViewModel::setSelectedConversationId(const QString &id)
{
    if (m_selectedConversationId != id) {
        m_selectedConversationId = id;
        emit selectedConversationChanged();
        emit conversationSelected(id);
    }
}

void ConversationListViewModel::createNewConversation()
{
    // Create a new conversation using the conversation manager
    QString id = m_conversationManager->createConversation("New Conversation");
    
    // Refresh the conversation list
    loadConversations();
    
    // Select the new conversation
    setSelectedConversationId(id);
    
    // Emit signal for conversation creation
    emit conversationCreated(id);
}

void ConversationListViewModel::deleteConversation(const QString &id)
{
    // Delete the conversation using the conversation manager
    if (m_conversationManager->deleteConversation(id)) {
        // Refresh the conversation list
        loadConversations();
        
        // If the deleted conversation was selected, select another one
        if (m_selectedConversationId == id) {
            if (!m_conversations.isEmpty()) {
                setSelectedConversationId(m_conversations.first().toMap()["id"].toString());
            } else {
                setSelectedConversationId("");
                // If no conversations left, create a new one
                createNewConversation();
            }
        }
        
        emit conversationDeleted(id);
    }
}

void ConversationListViewModel::renameConversation(const QString &id, const QString &newTitle)
{
    // Rename the conversation using the conversation manager
    if (m_conversationManager->renameConversation(id, newTitle)) {
        // Refresh the conversation list
        loadConversations();
        
        emit conversationRenamed(id, newTitle);
    }
}

void ConversationListViewModel::refreshConversations()
{
    loadConversations();
}

void ConversationListViewModel::selectConversation(const QString &id)
{
    setSelectedConversationId(id);
}

void ConversationListViewModel::exportConversation(const QString &id, const QString &format)
{
    // Convert format string to enum
    ExportService::ExportFormat exportFormat = m_exportService->stringToFormat(format);
    
    // Get suggested filename
    QString suggestedFilename = m_exportService->getSuggestedFilename(id, exportFormat);
    
    // Get default directory
    QString defaultDir = m_exportService->getDefaultExportDirectory();
    
    // Emit signal to request file dialog from QML
    emit requestExportFileDialog(id, defaultDir + "/" + suggestedFilename, 
                               m_exportService->getFileFilterString());
}

void ConversationListViewModel::showImportDialog()
{
    emit requestImportFileDialog();
}

void ConversationListViewModel::importConversation(const QString &filePath)
{
    if (filePath.isEmpty()) {
        return;
    }
    
    // Validate the file
    QString errorMessage;
    if (!m_importService->validateImportFile(filePath, errorMessage)) {
        emit importFailed(errorMessage);
        return;
    }
    
    // Emit import started signal
    emit importStarted();
    
    // Import the conversation
    QString conversationId;
    bool success = m_importService->importConversation(filePath);
    
    if (success) {
        // Refresh the conversation list
        loadConversations();
        
        emit importCompleted(conversationId);
    } else {
        emit importFailed("Failed to import conversation");
    }
}

void ConversationListViewModel::loadConversations()
{
    // Get all conversations from the conversation manager
    m_conversations = m_conversationManager->getAllConversations();
    
    // Sort conversations by timestamp (newest first)
    std::sort(m_conversations.begin(), m_conversations.end(), [](const QVariant &a, const QVariant &b) {
        QDateTime timeA = QDateTime::fromString(a.toMap()["timestamp"].toString(), Qt::ISODate);
        QDateTime timeB = QDateTime::fromString(b.toMap()["timestamp"].toString(), Qt::ISODate);
        return timeA > timeB;
    });
    
    emit conversationsChanged();
    
    // Select the first conversation if none is selected
    if (m_selectedConversationId.isEmpty() && !m_conversations.isEmpty()) {
        setSelectedConversationId(m_conversations.first().toMap()["id"].toString());
    }
}

void ConversationListViewModel::connectSignals()
{
    // Connect signals from conversation manager
    connect(m_conversationManager, &ConversationManager::conversationCreated,
            this, &ConversationListViewModel::refreshConversations);
    
    connect(m_conversationManager, &ConversationManager::conversationDeleted,
            this, &ConversationListViewModel::refreshConversations);
    
    connect(m_conversationManager, &ConversationManager::conversationRenamed,
            this, &ConversationListViewModel::refreshConversations);
    
    connect(m_conversationManager, &ConversationManager::conversationsLoaded,
            this, &ConversationListViewModel::refreshConversations);
    
    // Connect signals from export service
    connect(m_exportService, &ExportService::exportStarted,
            [this](const QString &id) { emit exportStarted(id); });
    
    connect(m_exportService, &ExportService::exportCompleted,
            [this](const QString &id, const QString &filePath) { emit exportCompleted(id, filePath); });
    
    connect(m_exportService, &ExportService::exportFailed,
            [this](const QString &id, const QString &error) { emit exportFailed(id, error); });
    
    // Connect signals from import service
    connect(m_importService, &ImportService::importStarted,
            [this](const QString &) { emit importStarted(); });
    
    connect(m_importService, &ImportService::importCompleted,
            [this](const QString &, const QString &conversationId) {
                emit importCompleted(conversationId);
                refreshConversations();
            });
    
    connect(m_importService, &ImportService::importFailed,
            [this](const QString &, const QString &error) { emit importFailed(error); });
}

void ConversationListViewModel::handleExportFileSelected(const QString &id, 
                                                       const QString &filePath,
                                                       const QString &format)
{
    if (filePath.isEmpty()) {
        return;
    }
    
    // Convert format string to enum
    ExportService::ExportFormat exportFormat = m_exportService->stringToFormat(format);
    
    // Emit export started signal
    emit exportStarted(id);
    
    // Export the conversation
    bool success = m_exportService->exportConversation(id, filePath, exportFormat);
    
    if (success) {
        emit exportCompleted(id, filePath);
    } else {
        emit exportFailed(id, "Failed to export conversation");
    }
}

void ConversationListViewModel::importConversation()
{
    // 调用带参数版本，传入空字符串
    importConversation(QString());
}
