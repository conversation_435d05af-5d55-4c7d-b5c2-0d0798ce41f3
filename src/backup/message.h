#ifndef MESSAGE_H
#define MESSAGE_H

#include <QString>
#include <QDateTime>
#include <QVariantMap>
#include <QList>
#include <QProperty>

class Attachment
{
public:
    enum Type {
        Image,
        PDF,
        Text,
        Other
    };

    QProperty<QString> id;
    QProperty<QString> filename;
    QProperty<QString> path;
    QProperty<Type> type;
    QProperty<QString> mimeType;
    QProperty<qint64> size;

    Attachment();
    Attachment(const QString &id, const QString &filename, const QString &path, Type type);
    
    // QString id() const;
    // void setId(const QString &id);
    //
    // QString filename() const;
    // void setFilename(const QString &filename);
    //
    // QString path() const;
    // void setPath(const QString &path);
    //
    // Type type() const;
    // void setType(Type type);
    //
    // QString mimeType() const;
    // void setMimeType(const QString &mimeType);
    //
    // qint64 size() const;
    // void setSize(qint64 size);
    //
    // QString typeString() const;
    // void setTypeFromString(const QString &typeStr);
    //
    // QVariantMap toVariantMap() const;
    // static Attachment fromVariantMap(const QVariantMap &map);
    // private:
};

class Message
{
public:
    enum Role {
        User,
        Assistant,
        System
    };

    QProperty<QString> id;
    QProperty<QString> content;
    QProperty<Role> role;
    QProperty<QDateTime> timestamp;
    QList<Attachment> attachments;

    Message();
    Message(const QString &id, const QString &content, Role role);
    
//     QString id() const;
//     void setId(const QString &id);
//
//     QString content() const;
//     void setContent(const QString &content);
//
//     Role role() const;
//     void setRole(Role role);
//
//     QString roleString() const;
//     void setRoleFromString(const QString &roleStr);
//
//     QDateTime timestamp() const;
//     void setTimestamp(const QDateTime &timestamp);
//
//     QList<Attachment> attachments() const;
//     void setAttachments(const QList<Attachment> &attachments);
//     void addAttachment(const Attachment &attachment);
//     void removeAttachment(const QString &attachmentId);
//     bool hasAttachments() const;
//
//     QVariantMap toVariantMap() const;
//     static Message fromVariantMap(const QVariantMap &map);
//
// private:
//     QString m_id;
//     QString m_content;
//     Role m_role;
//     QDateTime m_timestamp;
//     QList<Attachment> m_attachments;
};

#endif // MESSAGE_H