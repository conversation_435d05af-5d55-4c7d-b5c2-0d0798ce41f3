// #include "conversation.h"
// #include <QUuid>
//
// Conversation::Conversation()
//     : createdAt{QDateTime::currentDateTime()},
//       updatedAt{QDateTime::currentDateTime()}, provider{""}, model{""},
//       systemPrompt{""}, temperature{0.7}, maxTokens{2000}, isTemplate{false},
//       templateName("") {
//   id = QUuid::createUuid().toString(QUuid::WithoutBraces);
//   title = "New Conversation";
// }
//
// Conversation::Conversation(const QString &id, const QString &title)
//     : id{id}, title{title}, createdAt{QDateTime::currentDateTime()},
//       updatedAt{QDateTime::currentDateTime()}, provider{""}, model{""},
//       systemPrompt{""}, temperature{0.7}, maxTokens{2000},
//       isTemplate{false}, templateName{""} {}
//
// // QString Conversation::id() const
// // {
// //     return m_id;
// // }
// //
// // void Conversation::setId(const QString &id)
// // {
// //     m_id = id;
// // }
// //
// // QString Conversation::title() const
// // {
// //     return m_title;
// // }
// //
// // void Conversation::setTitle(const QString &title)
// // {
// //     m_title = title;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QDateTime Conversation::createdAt() const
// // {
// //     return m_createdAt;
// // }
// //
// // void Conversation::setCreatedAt(const QDateTime &dateTime)
// // {
// //     m_createdAt = dateTime;
// // }
// //
// // QDateTime Conversation::updatedAt() const
// // {
// //     return m_updatedAt;
// // }
// //
// // void Conversation::setUpdatedAt(const QDateTime &dateTime)
// // {
// //     m_updatedAt = dateTime;
// // }
// //
// // QString Conversation::provider() const
// // {
// //     return m_provider;
// // }
// //
// // void Conversation::setProvider(const QString &provider)
// // {
// //     m_provider = provider;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QString Conversation::model() const
// // {
// //     return m_model;
// // }
// //
// // void Conversation::setModel(const QString &model)
// // {
// //     m_model = model;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QString Conversation::systemPrompt() const
// // {
// //     return m_systemPrompt;
// // }
// //
// // void Conversation::setSystemPrompt(const QString &systemPrompt)
// // {
// //     m_systemPrompt = systemPrompt;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // double Conversation::temperature() const
// // {
// //     return m_temperature;
// // }
// //
// // void Conversation::setTemperature(double temperature)
// // {
// //     m_temperature = temperature;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // int Conversation::maxTokens() const
// // {
// //     return m_maxTokens;
// // }
// //
// // void Conversation::setMaxTokens(int maxTokens)
// // {
// //     m_maxTokens = maxTokens;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QVariantMap Conversation::settings() const
// // {
// //     return m_settings;
// // }
// //
// // void Conversation::setSettings(const QVariantMap &settings)
// // {
// //     m_settings = settings;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QVariant Conversation::getSetting(const QString &key, const QVariant
// // &defaultValue) const
// // {
// //     return m_settings.value(key, defaultValue);
// // }
// //
// // void Conversation::setSetting(const QString &key, const QVariant &value)
// // {
// //     m_settings[key] = value;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // bool Conversation::isTemplate() const
// // {
// //     return m_isTemplate;
// // }
// //
// // void Conversation::setIsTemplate(bool isTemplate)
// // {
// //     m_isTemplate = isTemplate;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QString Conversation::templateName() const
// // {
// //     return m_templateName;
// // }
// //
// // void Conversation::setTemplateName(const QString &name)
// // {
// //     m_templateName = name;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // QList<Message> Conversation::messages() const
// // {
// //     return m_messages;
// // }
// //
// // void Conversation::setMessages(const QList<Message> &messages)
// // {
// //     m_messages = messages;
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // void Conversation::addMessage(const Message &message)
// // {
// //     m_messages.append(message);
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // void Conversation::clearMessages()
// // {
// //     m_messages.clear();
// //     m_updatedAt = QDateTime::currentDateTime();
// // }
// //
// // int Conversation::messageCount() const
// // {
// //     return m_messages.size();
// // }