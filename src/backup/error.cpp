#include <iostream>
#include <system_error>
#include <concepts>
#include <string_view>
#include <array>

// 前向声明错误类别模板
template<typename E>
struct error_traits;

// 定义错误枚举的概念
template<typename E>
concept ErrorEnum = std::is_enum_v<E> && 
    requires {
        typename error_traits<E>::category_type;
        { error_traits<E>::category_name } -> std::convertible_to<std::string_view>;
        { error_traits<E>::message(E{}) } -> std::convertible_to<std::string_view>;
    };

// 基础错误类别模板
template<typename E>
class basic_error_category : public std::error_category {
public:
    const char* name() const noexcept override {
        return error_traits<E>::category_name.data();
    }
    
    std::string message(int ev) const override {
        return std::string(error_traits<E>::message(static_cast<E>(ev)));
    }
    
    static const basic_error_category& instance() {
        static basic_error_category instance;
        return instance;
    }
};

// 网络错误枚举
enum class network_error {
    success = 0,
    timeout,
    connection_refused,
    invalid_address
};

// 网络错误特化
template<>
struct error_traits<network_error> {
    using category_type = basic_error_category<network_error>;
    static constexpr std::string_view category_name = "network_error";
    
    static std::string_view message(network_error e) {
        static constexpr std::array messages = {
            "Success",
            "Operation timed out",
            "Connection refused",
            "Invalid network address"
        };
        return messages[static_cast<int>(e)];
    }
};

// 文件系统错误枚举
enum class filesystem_error {
    success = 0,
    file_not_found,
    permission_denied,
    disk_full
};

// 文件系统错误特化
template<>
struct error_traits<filesystem_error> {
    using category_type = basic_error_category<filesystem_error>;
    static constexpr std::string_view category_name = "filesystem_error";
    
    static std::string_view message(filesystem_error e) {
        static constexpr std::array messages = {
            "Success",
            "File not found",
            "Permission denied",
            "Disk full"
        };
        return messages[static_cast<int>(e)];
    }
};

// 通用的 make_error_code 模板
template<ErrorEnum E>
std::error_code make_error_code(E e) {
    return {static_cast<int>(e), error_traits<E>::category_type::instance()};
}

// 让 STL 知道这些枚举可以转换为 error_code
namespace std {
    template<> struct is_error_code_enum<network_error> : true_type {};
    template<> struct is_error_code_enum<filesystem_error> : true_type {};
}

// 使用示例
void example_usage() {
    // 直接从枚举创建 error_code（利用 is_error_code_enum 特化）
    std::error_code ec1 = network_error::timeout;
    
    // 显式使用 make_error_code
    std::error_code ec2 = make_error_code(filesystem_error::file_not_found);
    
    std::cout << "Network error: " << ec1.message() << std::endl;
    std::cout << "Category: " << ec1.category().name() << std::endl;
    std::cout << "Error code: " << ec1.value() << std::endl;
    std::cout << std::endl;
    
    std::cout << "Filesystem error: " << ec2.message() << std::endl;
    std::cout << "Category: " << ec2.category().name() << std::endl;
    std::cout << "Error code: " << ec2.value() << std::endl;
}

// 使用 expected 的示例（需要 C++23）
#include <expected>

std::expected<int, std::error_code> divide(int a, int b) {
    if (b == 0) {
        return std::unexpected(network_error::invalid_address);
    }
    return a / b;
}

int main() {
    example_usage();
    
    std::cout << std::endl << "Using expected:" << std::endl;
    
    auto result = divide(10, 2);
    if (result) {
        std::cout << "Result: " << *result << std::endl;
    } else {
        std::cout << "Error: " << result.error().message() << std::endl;
    }
    
    auto error_result = divide(10, 0);
    if (!error_result) {
        std::cout << "Error: " << error_result.error().message() << std::endl;
    }
    
    return 0;
}
