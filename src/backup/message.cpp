#include "../models/message.h"
#include <QFileInfo>
#include <QMimeDatabase>
#include <QUuid>

// Attachment implementation
Attachment::Attachment()
    : id{}, filename{}, path{}, type{}, mimeType{}, size{0} {
  id = QUuid::createUuid().toString(QUuid::WithoutBraces);
}

Attachment::Attachment(const QString &id_, const QString &filename_,
                       const QString &path_, Type type_)
    : id{id_}, filename{filename_}, path{path_}, type{type_}, size{0} {
  // Determine mime type from file extension
  QMimeDatabase db;
  QFileInfo fileInfo(filename);
  mimeType =
      db.mimeTypeForFile(fileInfo.fileName(), QMimeDatabase::MatchExtension)
          .name();

  // Get file size if path is valid
  QFileInfo pathInfo(path);
  if (pathInfo.exists()) {
    size = pathInfo.size();
  }
}

// QString Attachment::id() const { return m_id; }
//
// void Attachment::setId(const QString &id) { m_id = id; }
//
// QString Attachment::filename() const { return m_filename; }
//
// void Attachment::setFilename(const QString &filename) {
//   m_filename = filename;
//
//   // Update mime type when filename changes
//   QMimeDatabase db;
//   QFileInfo fileInfo(filename);
//   m_mimeType =
//       db.mimeTypeForFile(fileInfo.fileName(), QMimeDatabase::MatchExtension)
//           .name();
// }
//
// QString Attachment::path() const { return m_path; }
//
// void Attachment::setPath(const QString &path) {
//   m_path = path;
//
//   // Update file size when path changes
//   QFileInfo fileInfo(path);
//   if (fileInfo.exists()) {
//     m_size = fileInfo.size();
//   }
// }
//
// Attachment::Type Attachment::type() const { return m_type; }
//
// void Attachment::setType(Type type) { m_type = type; }
//
// QString Attachment::mimeType() const { return m_mimeType; }
//
// void Attachment::setMimeType(const QString &mimeType) { m_mimeType =
// mimeType; }
//
// qint64 Attachment::size() const { return m_size; }
//
// void Attachment::setSize(qint64 size) { m_size = size; }
//
// QString Attachment::typeString() const {
//   switch (m_type) {
//   case Image:
//     return "image";
//   case PDF:
//     return "pdf";
//   case Text:
//     return "text";
//   case Other:
//     return "other";
//   default:
//     return "other";
//   }
// }
//
// void Attachment::setTypeFromString(const QString &typeStr) {
//   if (typeStr == "image") {
//     m_type = Image;
//   } else if (typeStr == "pdf") {
//     m_type = PDF;
//   } else if (typeStr == "text") {
//     m_type = Text;
//   } else {
//     m_type = Other;
//   }
// }
//
// QVariantMap Attachment::toVariantMap() const {
//   QVariantMap map;
//   map["id"] = m_id;
//   map["filename"] = m_filename;
//   map["path"] = m_path;
//   map["type"] = typeString();
//   map["mimeType"] = m_mimeType;
//   map["size"] = m_size;
//   return map;
// }
//
// Attachment Attachment::fromVariantMap(const QVariantMap &map) {
//   Attachment attachment;
//   attachment.setId(map["id"].toString());
//   attachment.setFilename(map["filename"].toString());
//   attachment.setPath(map["path"].toString());
//   attachment.setTypeFromString(map["type"].toString());
//   attachment.setMimeType(map["mimeType"].toString());
//   attachment.setSize(map["size"].toLongLong());
//   return attachment;
// }
//
// // Message implementation
// Message::Message() : m_role(User), m_timestamp(QDateTime::currentDateTime())
// {
//   m_id = QUuid::createUuid().toString(QUuid::WithoutBraces);
// }
//
// Message::Message(const QString &id, const QString &content, Role role)
//     : m_id(id), m_content(content), m_role(role),
//       m_timestamp(QDateTime::currentDateTime()) {}
//
// QString Message::id() const { return m_id; }
//
// void Message::setId(const QString &id) { m_id = id; }
//
// QString Message::content() const { return m_content; }
//
// void Message::setContent(const QString &content) { m_content = content; }
//
// Message::Role Message::role() const { return m_role; }
//
// void Message::setRole(Role role) { m_role = role; }
//
// QString Message::roleString() const {
//   switch (m_role) {
//   case User:
//     return "user";
//   case Assistant:
//     return "assistant";
//   case System:
//     return "system";
//   default:
//     return "user";
//   }
// }
//
// void Message::setRoleFromString(const QString &roleStr) {
//   if (roleStr == "assistant") {
//     m_role = Assistant;
//   } else if (roleStr == "system") {
//     m_role = System;
//   } else {
//     m_role = User;
//   }
// }
//
// QDateTime Message::timestamp() const { return m_timestamp; }
//
// void Message::setTimestamp(const QDateTime &timestamp) {
//   m_timestamp = timestamp;
// }
//
// QList<Attachment> Message::attachments() const { return m_attachments; }
//
// void Message::setAttachments(const QList<Attachment> &attachments) {
//   m_attachments = attachments;
// }
//
// void Message::addAttachment(const Attachment &attachment) {
//   m_attachments.append(attachment);
// }
//
// void Message::removeAttachment(const QString &attachmentId) {
//   for (int i = 0; i < m_attachments.size(); ++i) {
//     if (m_attachments.at(i).id() == attachmentId) {
//       m_attachments.removeAt(i);
//       break;
//     }
//   }
// }
//
// bool Message::hasAttachments() const { return !m_attachments.isEmpty(); }
//
// QVariantMap Message::toVariantMap() const {
//   QVariantMap map;
//   map["id"] = m_id;
//   map["content"] = m_content;
//   map["role"] = roleString();
//   map["timestamp"] = m_timestamp.toString(Qt::ISODate);
//
//   // Add attachments if any
//   if (!m_attachments.isEmpty()) {
//     QVariantList attachmentsList;
//     for (const Attachment &attachment : m_attachments) {
//       attachmentsList.append(attachment.toVariantMap());
//     }
//     map["attachments"] = attachmentsList;
//   }
//
//   return map;
// }
//
// Message Message::fromVariantMap(const QVariantMap &map) {
//   Message message;
//   message.setId(map["id"].toString());
//   message.setContent(map["content"].toString());
//   message.setRoleFromString(map["role"].toString());
//
//   QDateTime timestamp =
//       QDateTime::fromString(map["timestamp"].toString(), Qt::ISODate);
//   if (timestamp.isValid()) {
//     message.setTimestamp(timestamp);
//   }
//
//   // Parse attachments if available
//   if (map.contains("attachments")) {
//     QVariantList attachmentsList = map["attachments"].toList();
//     for (const QVariant &attachmentVar : attachmentsList) {
//       QVariantMap attachmentMap = attachmentVar.toMap();
//       message.addAttachment(Attachment::fromVariantMap(attachmentMap));
//     }
//   }
//
//   return message;
// }