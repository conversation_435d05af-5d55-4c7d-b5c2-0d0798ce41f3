// #ifndef CONVERSATION_H
// #define CONVERSATION_H
//
// #include <QString>
// #include <QList>
// #include "message.h"
//
// class Conversation
// {
// public:
//     QProperty<QString> id;
//     QProperty<QString> title;
//     QProperty<QDateTime> createdAt;
//     QProperty<QDateTime> updatedAt;
//     QProperty<QString> provider;
//     QProperty<QString> model;
//     QProperty<QString> systemPrompt;
//     QProperty<double> temperature;
//     QProperty<int> maxTokens;
//     QProperty<QVariantMap> settings;
//     QProperty<bool> isTemplate;
//     QProperty<QString> templateName;
//     QList<Message> messages;
//
//     Conversation();
//     Conversation(const QString &id, const QString &title);
//
//     //
//     // QString id() const;
//     // void setId(const QString &id);
//     //
//     // QString title() const;
//     // void setTitle(const QString &title);
//     //
//     // QDateTime createdAt() const;
//     // void setCreatedAt(const QDateTime &dateTime);
//     //
//     // QDateTime updatedAt() const;
//     // void setUpdatedAt(const QDateTime &dateTime);
//     //
//     // QString provider() const;
//     // void setProvider(const QString &provider);
//     //
//     // QString model() const;
//     // void setModel(const QString &model);
//     //
//     // QString systemPrompt() const;
//     // void setSystemPrompt(const QString &systemPrompt);
//     //
//     // // Conversation settings
//     // double temperature() const;
//     // void setTemperature(double temperature);
//     //
//     // int maxTokens() const;
//     // void setMaxTokens(int maxTokens);
//     //
//     // // Additional settings as a map for flexibility
//     // QVariantMap settings() const;
//     // void setSettings(const QVariantMap &settings);
//     // QVariant getSetting(const QString &key, const QVariant &defaultValue = QVariant()) const;
//     // void setSetting(const QString &key, const QVariant &value);
//     //
//     // // Template support
//     // bool isTemplate() const;
//     // void setIsTemplate(bool isTemplate);
//     //
//     // QString templateName() const;
//     // void setTemplateName(const QString &name);
//     //
//     // QList<Message> messages() const;
//     // void setMessages(const QList<Message> &messages);
//     //
//     // void addMessage(const Message &message);
//     // void clearMessages();
//     //
//     // int messageCount() const;
// //
// // private:
// //     QString m_id;
// //     QString m_title;
// //     QDateTime m_createdAt;
// //     QDateTime m_updatedAt;
// //     QString m_provider;
// //     QString m_model;
// //     QString m_systemPrompt;
// //     double m_temperature;
// //     int m_maxTokens;
// //     QVariantMap m_settings;
// //     bool m_isTemplate;
// //     QString m_templateName;
// //     QList<Message> m_messages;
// };
//
// #endif // CONVERSATION_H