#pragma once

#include <system_error>

#include <QQmlApplicationEngine>
#include <gsl/gsl>

#include <services/database_service.h>

namespace ChamberUI::Bootstrap {
using namespace Services;

class BootstrapManager : public QObject {
  Q_OBJECT;

public:
  BootstrapManager(const BootstrapManager &) = delete;
  std::error_code initializeServices();
  QQmlApplicationEngine &getQmlEngine() { return engine_; }

private:
  explicit  BootstrapManager(QObject *parent = nullptr);
  QQmlApplicationEngine engine_;
  gsl::owner<DatabaseService *> database_service_;
};

} // namespace ChamberUI::Bootstrap
