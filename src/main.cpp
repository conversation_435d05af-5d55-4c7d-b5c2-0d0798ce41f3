#include <QGuiApplication>
#include <QQmlApplicationEngine>
#include <QQmlContext>
#include <QFontDatabase>
#include <QFile>
#include <QDebug>

#include "backup/conversation.h"
#include "backup/message.h"
#include "services/messageprocessor.h"
#include "services/providermanager.h"
#include "services/updateservice.h"
#include "services/voiceservice.h"
#include "viewmodels/appviewmodel.h"
#include "viewmodels/chatviewmodel.h"
#include "viewmodels/conversationlistviewmodel.h"
#include "viewmodels/settingsviewmodel.h"

int main(int argc, char *argv[])
{
    QGuiApplication app(argc, argv);

    // Set application information
    app.setOrganizationName("Soloholic");
    app.setOrganizationDomain("soloholic.com");
    app.setApplicationName("ChamberUI");
    app.setApplicationVersion("1.0.0");

    // 注册C++类型到QML
    qmlRegisterType<ChatViewModel>("ChamberUI.ViewModels", 1, 0, "ChatViewModel");
    qmlRegisterType<AppViewModel>("ChamberUI.ViewModels", 1, 0, "AppViewModel");
    qmlRegisterType<ConversationListViewModel>("ChamberUI.ViewModels", 1, 0, "ConversationListViewModel");
    qmlRegisterType<Conversation>("ChamberUI.Models", 1, 0, "Conversation");
    qmlRegisterType<Message>("ChamberUI.Models", 1, 0, "Message");

    // 注册只读类型
    // qmlRegisterUncreatableType<MessageRole>("ChamberUI.Models", 1, 0, "MessageRole",
    //     "MessageRole is an enum type");

    // 对于需要单例访问的类型，可以注册为单例
    // qmlRegisterSingletonType<ProviderManager>("ChamberUI.Services", 1, 0, "ProviderManager",
    //     [](QQmlEngine *engine, QJSEngine *scriptEngine) -> QObject * {
    //         Q_UNUSED(engine)
    //         Q_UNUSED(scriptEngine)
    //         return ProviderManager::instance();
    //     });

    // Initialize view models
    AppViewModel appViewModel;
    ChatViewModel chatViewModel;
    ConversationListViewModel conversationListViewModel;
    SettingsViewModel settingsViewModel;
    VoiceService voiceService;
    UpdateService updateService;

    // Connect view models
    QObject::connect(&conversationListViewModel, &ConversationListViewModel::conversationSelected,
                    &chatViewModel, &ChatViewModel::loadConversation);

    // Connect chat view model to settings view model
    QObject::connect(&chatViewModel, &ChatViewModel::messageSent,
                    [&settingsViewModel](const QString &conversationId, const QString &content) {
                        // This is where we would save the message to the database
                        // For now, we just make sure the settings are applied
                        settingsViewModel.saveSettings();
                    });

    // Set up QML engine
    QQmlApplicationEngine engine;

    // Register view models with QML
    engine.rootContext()->setContextProperty("appViewModel", &appViewModel);
    engine.rootContext()->setContextProperty("globalChatViewModel", &chatViewModel);
    engine.rootContext()->setContextProperty("conversationListViewModel", &conversationListViewModel);
    engine.rootContext()->setContextProperty("settingsViewModel", &settingsViewModel);
    engine.rootContext()->setContextProperty("voiceService", &voiceService);
    engine.rootContext()->setContextProperty("updateService", &updateService);

    // Load main QML file
    // Try different approaches to find the QML file
    QUrl url{QStringLiteral("qrc:/qml/main.qml")};

    QObject::connect(&engine, &QQmlApplicationEngine::objectCreated,
                     &app, [url](QObject *obj, const QUrl &objUrl) {
        if (!obj && url == objUrl)
            QCoreApplication::exit(-1);
    }, Qt::QueuedConnection);

    engine.load(url);

    return app.exec();
}