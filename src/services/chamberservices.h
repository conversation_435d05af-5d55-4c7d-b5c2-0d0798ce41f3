#ifndef CHAMBERSERVICES_H
#define CHAMBERSERVICES_H

#include "chamberservices_global.h"

// 包含所有服务类的头文件
#include "anthropic_provider.h"
#include "cacheservice.h"
#include "conversationmanager.h"
#include "database_service.h"
#include "exportservice.h"
#include "importservice.h"
#include "messageprocessor.h"
#include "openai_provider.h"
#include "provider.h"
#include "providermanager.h"
#include "updateservice.h"
#include "voiceservice.h"

// 版本信息
#define CHAMBERSERVICES_VERSION_MAJOR 0
#define CHAMBERSERVICES_VERSION_MINOR 1
#define CHAMBERSERVICES_VERSION_PATCH 0

namespace ChamberServices {
    // 返回库的版本信息
    inline QString version() {
        return QString("%1.%2.%3")
            .arg(CHAMBERSERVICES_VERSION_MAJOR)
            .arg(CHAMBERSERVICES_VERSION_MINOR)
            .arg(CHAMBERSERVICES_VERSION_PATCH);
    }
}

#endif // CHAMBERSERVICES_H
