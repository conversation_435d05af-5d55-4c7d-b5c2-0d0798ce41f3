#include "database_service.h"

#include <magic_enum.hpp>

#include <QCryptographicHash>
#include <QDateTime>
#include <QDebug>
#include <QDir>
#include <QJsonObject>
#include <QRandomGenerator>
#include <QSqlError>
#include <QSqlQuery>
#include <QStandardPaths>
#include <QString>

#include "errors/DatabaseErrors.h"
#include "services/Utility.h"

namespace ChamberUI::Services {

using namespace Errors;

struct ConvFields {
  static constexpr std::string_view id = "id";
  static constexpr std::string_view title = "title";
  static constexpr std::string_view create_at = "create_at";
  static constexpr std::string_view update_at = "update_at";
  static constexpr std::string_view provider = "provider";
  static constexpr std::string_view model = "model";
  static constexpr std::string_view system_prompt = "system_prompt";
  static constexpr std::string_view temperature = "temperature";
  static constexpr std::string_view max_tokens = "max_tokens";
  static constexpr std::string_view is_template = "is_template";
  static constexpr std::string_view template_name = "template_name";
  static constexpr std::string_view settings = "settings";
  static std::string_view all_fields() {
    constexpr std::string_view fields[] = {
        id,         title,       create_at,     update_at,
        provider,   model,       system_prompt, temperature,
        max_tokens, is_template, template_name, settings};
    return join_fields_once(fields);
  }
};

struct MsgFields {
  static constexpr std::string_view id = "id";
  static constexpr std::string_view conversation_id = "conversation_id";
  static constexpr std::string_view content = "content";
  static constexpr std::string_view role = "role";
  static constexpr std::string_view timestamp = "timestamp";
  static constexpr std::string_view metadata = "metadata";
  static std::string_view all_fields() {
    constexpr std::string_view fields[] = {id,   conversation_id, content,
                                           role, timestamp,       metadata};
    return join_fields_once(fields);
  }
};

struct AtthFields {
  static constexpr std::string_view id = "id";
  static constexpr std::string_view message_id = "message_id";
  static constexpr std::string_view conversation_id = "conversation_id";
  static constexpr std::string_view filename = "filename";
  static constexpr std::string_view path = "path";
  static constexpr std::string_view type = "type";
  static constexpr std::string_view mime_type = "mime_type";
  static constexpr std::string_view size = "size";
  static std::string_view all_fields() {
    constexpr std::string_view fields[] = {
        id, message_id, conversation_id, filename, path, type, mime_type, size};
    return join_fields_once(fields);
  }
};

struct SettingFields {
  static constexpr std::string_view key = "key";
  static constexpr std::string_view value = "value";
  static constexpr std::string_view updated_at = "updated_at";
  static std::string_view all_fields() {
    constexpr std::string_view fields[] = {key, value, updated_at};
    return join_fields_once(fields);
  }
};

struct ProviderFields {
  static constexpr std::string_view provider = "provider";
  static constexpr std::string_view encrypted_key = "encrypted_key";
  static constexpr std::string_view updated_at = "updated_at";
  static std::string_view all_fields() {
    constexpr std::string_view fields[] = {provider, encrypted_key, updated_at};
    return join_fields_once(fields);
  }
};

struct Helper {
  [[nodiscard]] static ConversationPtr
  createConversationFromQuery(const QSqlQuery &query) {
    ConversationPtr result = std::make_unique<Conversation>();

    result->id = query.value(ConvFields::id).toString();
    result->title = query.value(ConvFields::title).toString();
    result->created_at = query.value(ConvFields::create_at).toDateTime();
    result->updated_at = query.value(ConvFields::update_at).toDateTime();
    result->provider = query.value(ConvFields::provider).toString();
    result->model = query.value(ConvFields::model).toString();
    result->system_prompt = query.value(ConvFields::system_prompt).toString();
    result->temperature = query.value(ConvFields::temperature).toDouble();
    result->max_tokens = query.value(ConvFields::max_tokens).toInt();
    result->is_template = query.value(ConvFields::is_template).toBool();
    result->template_name = query.value(ConvFields::template_name).toString();

    // Parse settings if available
    QString settingsStr = query.value(ConvFields::settings).toString();
    if (!settingsStr.isEmpty()) {
      QJsonDocument doc = QJsonDocument::fromJson(settingsStr.toUtf8());
      if (!doc.isNull() && doc.isObject()) {
        result->settings = doc.object().toVariantMap();
      }
    }
    return result;
  }
};

DatabaseService::DatabaseService(gsl::not_null<QObject *> parent)
    : QObject{parent}, db_{}, init_ec_{} {
  init_ec_ = initialize();
}

std::expected<gsl::not_null<DatabaseService *>, std::error_code>
DatabaseService::createDatabaseService(gsl::not_null<QObject *> *parent) {
  std::unique_ptr<DatabaseService> ds =
      std::unique_ptr<DatabaseService>{new DatabaseService{*parent}};
  std::error_code ec = ds->init_ec_;
  if (!ec) {
    return ds.release();
  } else {
    ds->setParent(nullptr);
    return std::unexpected(ec);
  }
}

DatabaseService::~DatabaseService() { closeDatabase(); }

std::error_code DatabaseService::initialize() {
  if (std::error_code ec = openDatabase()) {
    emit databaseError("Failed to open database");
    return ec;
  }

  if (std::error_code ec = createTables()) {
    emit databaseError("Failed to create database tables");
    closeDatabase();
    return ec;
  }

  emit databaseInitialized();

  return {};
}

std::expected<ConversationListPtr, std::error_code>
DatabaseService::getAllConversations() const {

  ConversationListPtr result = std::make_unique<QList<ConversationPtr>>();

  QSqlQuery query(db_);
  query.prepare("SELECT " + ConvFields::all_fields() +
                "FROM conversations ORDER BY updated_at DESC");

  if (!query.exec()) {
    qWarning() << "Failed to get conversations:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  while (query.next()) {
    ConversationPtr conv = Helper::createConversationFromQuery(query);
    // Get message count
    QSqlQuery countQuery(db_);
    countQuery.prepare(
        "SELECT COUNT(*) FROM messages WHERE conversation_id = ?");
    countQuery.addBindValue(conv->id);

    if (countQuery.exec() && countQuery.next()) {
      conv->message_count = countQuery.value(0).toInt();
    } else {
      conv->message_count = 0;
    }

    result->append(std::move(conv));
  }

  return result;
}

std::expected<ConversationPtr, std::error_code>
DatabaseService::getConversation(const QString &id) const {

  QSqlQuery query(db_);
  query.prepare("SELECT " + ConvFields::all_fields() + " FROM conversations WHERE id = ?");
  query.addBindValue(id);

  if (!query.exec() || !query.next()) {
    qWarning() << "Failed to get conversation:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  ConversationPtr result = Helper::createConversationFromQuery(query);

  // Get messages
  if (auto r = getConversationMessages(id)) {
    result->messages = std::move(*r);
    return result;
  } else {
    return std::unexpected(r.error());
  }
}

std::expected<MessageListPtr, std::error_code>
DatabaseService::getConversationMessages(const QString &id) const {

  QSqlQuery query(db_);
  query.prepare(
      "SELECT " + MsgFields::all_fields() +
      "FROM messages WHERE conversation_id = ? ORDER BY timestamp ASC");
  query.addBindValue(id);

  if (!query.exec()) {
    qWarning() << "Failed to get messages:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  MessageListPtr result = std::make_unique<QList<MessagePtr>>();
  while (query.next()) {
    MessagePtr message = std::make_unique<Message>();

    auto role_value = magic_enum::enum_cast<Message::Role>(
        query.value(MsgFields::role).toString().toStdString(),
        magic_enum::case_insensitive);

    if (!role_value.has_value()) {
      return std::unexpected(DatabaseError::DB_DATA_CORRUPTION);
    }

    message->id = query.value(MsgFields::id).toString();
    message->content = query.value(MsgFields::content).toString();
    message->role = role_value.value();
    message->timestamp = query.value(MsgFields::timestamp).toDateTime();

    // Parse metadata if available
    QString metadataStr = query.value(MsgFields::metadata).toString();
    if (!metadataStr.isEmpty()) {
      QJsonDocument doc = QJsonDocument::fromJson(metadataStr.toUtf8());
      if (!doc.isNull() && doc.isObject()) {
        message->metadata = doc.object().toVariantMap();
      }
    }

    // Get attachments for this message
    if (std::expected<AttachmentListPtr, std::error_code> atth_result =
            getMessageAttachments(message->id)) {
      message->attachments = std::move(atth_result.value());
    } else {
      return std::unexpected(atth_result.error());
    }

    result->append(message);
  }

  return result;
}

std::error_code DatabaseService::saveConversation(const Conversation &conv) {

  if (conv.id.isEmpty()) {
    return DatabaseError::DB_TABLE_KEY_LOST;
  }

  // Process settings if available
  QString settings_str{};
  if (!conv.settings.empty()) {
    QJsonDocument doc = QJsonDocument::fromVariant(conv.settings);
    settings_str = QString::fromUtf8(doc.toJson(QJsonDocument::Compact));
  }

  // clang-format off
  using CF = ConvFields;
  constexpr static std::string_view upsert_sql{
      "INSERT INTO conversations (" + CF::all_fields() +
      ") VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT(id) DO UPDATE SET " +
      CF::title         +"=excluded." + CF::title         + "," +
      CF::update_at     +"=excluded." + CF::update_at     + "," +
      CF::provider      +"=excluded." + CF::provider      + "," +
      CF::model         +"=excluded." + CF::model         + "," +
      CF::system_prompt +"=excluded." + CF::system_prompt + "," +
      CF::temperature   +"=excluded." + CF::temperature   + "," +
      CF::max_tokens    +"=excluded." + CF::max_tokens    + "," +
      CF::is_template   +"=excluded." + CF::is_template   + "," +
      CF::template_name +"=excluded." + CF::template_name + "," +
      CF::settings      +"=excluded." + CF::settings};
  // clang-format on

  QSqlQuery upsert_query(db_);
  upsert_query.prepare(QString::fromStdString(std::string{upsert_sql}));
  upsert_query.addBindValue(conv.id);
  upsert_query.addBindValue(conv.title);
  upsert_query.addBindValue(conv.created_at);
  upsert_query.addBindValue(conv.updated_at);
  upsert_query.addBindValue(conv.provider);
  upsert_query.addBindValue(conv.model);
  upsert_query.addBindValue(conv.system_prompt);
  upsert_query.addBindValue(conv.temperature);
  upsert_query.addBindValue(conv.max_tokens);
  upsert_query.addBindValue(conv.is_template);
  upsert_query.addBindValue(conv.template_name);
  upsert_query.addBindValue(settings_str);

  if (!upsert_query.exec()) {
    qWarning() << "Failed to save/update conversation:"
               << upsert_query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  if (conv.messages || conv.messages->empty()) {
    return {};
  }

  for (const auto &msg : *conv.messages) {
    msg->conversation_id = conv.id;
  }

  if (std::error_code ec = saveMessages(*conv.messages)) {
    qWarning() << "Failed to save message for conversation: " << conv.id;
    return ec;
  }

  return {};
}

std::error_code DatabaseService::deleteConversation(const QString &id) {

  // Delete messages first
  QSqlQuery deleteMessagesQuery(db_);
  deleteMessagesQuery.prepare("DELETE FROM messages WHERE conversation_id = ?");
  deleteMessagesQuery.addBindValue(id);

  if (!deleteMessagesQuery.exec()) {
    qWarning() << "Failed to delete messages: "
               << deleteMessagesQuery.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  // Delete conversation
  QSqlQuery deleteConversationQuery(db_);
  deleteConversationQuery.prepare("DELETE FROM conversations WHERE id = ?");
  deleteConversationQuery.addBindValue(id);

  if (!deleteConversationQuery.exec()) {
    qWarning() << "Failed to delete conversation: "
               << deleteConversationQuery.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::error_code DatabaseService::clearAllConversations() {

  // Delete all messages
  QSqlQuery deleteMessagesQuery(db_);
  if (!deleteMessagesQuery.exec("DELETE FROM messages")) {
    qWarning() << "Failed to delete all messages:"
               << deleteMessagesQuery.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  // Delete all conversations
  QSqlQuery deleteConversationsQuery(db_);
  if (!deleteConversationsQuery.exec("DELETE FROM conversations")) {
    qWarning() << "Failed to delete all conversations:"
               << deleteConversationsQuery.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::error_code
DatabaseService::saveMessages(const QList<MessagePtr> &messages) {
  if (messages.empty()) {
    return {};
  }

  // clang-format off
  using MSG = MsgFields;
  constexpr static std::string_view upsert_sql{
    "INSERT INTO messages (" + MSG::all_fields() +
    ") VALUES (?, ?, ?, ?, ?, ?) ON CONFLICT(id) DO UPDATE SET " +
    MSG::conversation_id + "=excluded." + MSG::conversation_id + "," +
    MSG::content         + "=excluded." + MSG::content         + "," +
    MSG::role            + "=excluded." + MSG::role            + "," +
    MSG::timestamp       + "=excluded." + MSG::timestamp       + "," +
    MSG::metadata        + "=excluded." + MSG::metadata
  };
  // clang-format on

  QSqlQuery upsert_query(db_);
  if (!db_.transaction()) {
    qWarning() << "Failed to begin transaction when saving messages.";
    return DatabaseError::DB_TRANSACTION_BEGIN_FAILED;
  }
  upsert_query.prepare(QString::fromStdString(std::string{upsert_sql}));
  for (auto &msg : messages) {
    upsert_query.addBindValue(msg->id);
    upsert_query.addBindValue(msg->conversation_id);
    upsert_query.addBindValue(msg->content);
    upsert_query.addBindValue(msg->role);
    upsert_query.addBindValue(msg->timestamp);
    upsert_query.addBindValue(msg->metadata);

    if (!upsert_query.exec()) {
      qWarning() << "Failed to upsert message for conversation: "
                 << msg->conversation_id;
      db_.rollback();
      return DatabaseError::DB_QUERY_FAILED;
    }

    if (!msg->attachments) {
      continue;
    }

    if (std::error_code ec = saveAttachments(*msg->attachments, true)) {
      qWarning() << "Failed to save attachments for message: " + msg->id;
      db_.rollback();
      return ec;
    }
  }

  if (!db_.commit()) {
    qWarning() << "Failed to commit transaction when saving messages.";
    return DatabaseError::DB_TRANSACTION_COMMIT_FAILED;
  }

  return {};
}

std::error_code DatabaseService::deleteMessage(const QString &conversation_id,
                                               const QString &message_id) {

  // Delete attachments first (foreign key constraint will handle this
  // automatically, but we'll do it explicitly for clarity)
  deleteAttachments(message_id);

  QSqlQuery del_msg_query(db_);
  del_msg_query.prepare(
      "DELETE FROM messages WHERE id = ? AND conversation_id = ?");
  del_msg_query.addBindValue(message_id);
  del_msg_query.addBindValue(conversation_id);

  if (!del_msg_query.exec()) {
    qWarning() << "Failed to delete message:"
               << del_msg_query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  // Update conversation updated_at timestamp
  QSqlQuery update_conv_query(db_);
  update_conv_query.prepare(
      "UPDATE conversations SET updated_at = ? WHERE id = ?");
  update_conv_query.addBindValue(
      QDateTime::currentDateTime().toString(Qt::ISODate));
  update_conv_query.addBindValue(conversation_id);

  if (!update_conv_query.exec()) {
    qWarning() << "Failed to update conversation timestamp:"
               << update_conv_query.lastError().text();
  }

  return {};
}

std::error_code
DatabaseService::deleteAttachment(const QString &attachment_id) {

  QSqlQuery query(db_);
  query.prepare("DELETE FROM attachments WHERE id = ?");
  query.addBindValue(attachment_id);

  if (!query.exec()) {
    qWarning() << "Failed to delete attachment:" << query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::error_code DatabaseService::deleteAttachments(const QString &message_id) {

  QSqlQuery query(db_);
  query.prepare("DELETE FROM attachments WHERE message_id = ?");
  query.addBindValue(message_id);

  if (!query.exec()) {
    qWarning() << "Failed to delete message attachments:"
               << query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::expected<AttachmentListPtr, std::error_code>
DatabaseService::getMessageAttachments(const QString &message_id) const {

  QSqlQuery query(db_);
  query.prepare("SELECT " + AtthFields::all_fields() +
                " FROM attachments WHERE message_id = ?");
  query.addBindValue(message_id);

  if (!query.exec()) {
    qWarning() << "Failed to get attachments:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  AttachmentListPtr result = std::make_unique<QList<AttachmentPtr>>();
  while (query.next()) {
    AttachmentPtr atth = std::make_unique<Attachment>();

    auto type_value = magic_enum::enum_cast<Attachment::Type>(
        query.value(AtthFields::type).toString().toStdString(),
        magic_enum::case_insensitive);
    if (!type_value.has_value()) {
      return std::unexpected(DatabaseError::DB_DATA_CORRUPTION);
    }

    atth->id = query.value(AtthFields::id).toString();
    atth->message_id = message_id;
    atth->filename = query.value(AtthFields::filename).toString();
    atth->path = query.value(AtthFields::path).toString();
    atth->type = type_value.value();
    atth->mime_type = query.value(AtthFields::mime_type).toString();
    atth->size = query.value(AtthFields::size).toLongLong();

    result->append(std::move(atth));
  }

  return result;
}
std::error_code
DatabaseService::saveAttachments(const QList<AttachmentPtr> &attachments,
                                 bool in_transaction) {
  if (attachments.empty()) {
    return {};
  }

  // clang-format off
  using AH = AtthFields;
  constexpr static std::string_view upsert_sql{
    "INSERT INTO attachments (" + AH::all_fields() +
    ") VALUES (?, ?, ?, ?, ?, ?, ?, ?) ON CONFLICT(id) DO UPDATE SET " +
    AH::message_id      + "=excluded." + AH::message_id      + "," +
    AH::conversation_id + "=excluded." + AH::conversation_id + "," +
    AH::filename        + "=excluded." + AH::filename         + "," +
    AH::path            + "=excluded." + AH::path             + "," +
    AH::type            + "=excluded." + AH::type             + "," +
    AH::mime_type       + "=excluded." + AH::mime_type        + "," +
    AH::size            + "=excluded." + AH::size
  };
  // clang-format on

  QSqlQuery upsert_query(db_);
  if (!in_transaction && !db_.transaction()) {
    qWarning() << "Failed to begin transaction when saving attachments.";
    return DatabaseError::DB_TRANSACTION_BEGIN_FAILED;
  }
  upsert_query.prepare(QString::fromStdString(std::string{upsert_sql}));
  for (auto &ath : attachments) {
    upsert_query.addBindValue(ath->id);
    upsert_query.addBindValue(ath->message_id);
    upsert_query.addBindValue(ath->filename);
    upsert_query.addBindValue(ath->conversation_id);
    upsert_query.addBindValue(ath->path);
    upsert_query.addBindValue(ath->type);
    upsert_query.addBindValue(ath->mime_type);
    upsert_query.addBindValue(ath->size);

    if (!upsert_query.exec()) {
      qWarning() << "Failed to upsert attachment: " << ath->id;
      if (!in_transaction) {
        db_.rollback();
      }
      return DatabaseError::DB_QUERY_FAILED;
    }
  }

  if (!in_transaction && !db_.commit()) {
    qWarning() << "Failed to commit transaction when saving attachments.";
    return DatabaseError::DB_TRANSACTION_COMMIT_FAILED;
  }

  return {};
}

std::error_code
DatabaseService::deleteMessages(const QString &conversation_id) {

  {
    QSqlQuery delete_msg_query{db_};
    delete_msg_query.prepare("DELETE FROM messages WHERE conversation_id = ?");
    delete_msg_query.addBindValue(conversation_id);
    if (!delete_msg_query.exec()) {
      qWarning() << "Failed to delete messages of conversation: "
                 << conversation_id;
      return DatabaseError::DB_QUERY_FAILED;
    }
  }

  {
    QSqlQuery delete_atth_query{db_};
    delete_atth_query.prepare(
        "DELETE FROM attachments WHERE conversation_id = ?");
    delete_atth_query.addBindValue(conversation_id);
    if (!delete_atth_query.exec()) {
      qWarning() << "Failed to delete attachments of conversation: "
                 << conversation_id;
      return DatabaseError::DB_QUERY_FAILED;
    }
  }

  {
    // Update conversation updated_at timestamp
    QSqlQuery update_conv_query(db_);
    update_conv_query.prepare(
        "UPDATE conversations SET updated_at = ? WHERE id = ?");
    update_conv_query.addBindValue(
        QDateTime::currentDateTime().toString(Qt::ISODate));
    update_conv_query.addBindValue(conversation_id);

    if (!update_conv_query.exec()) {
      qWarning() << "Failed to update conversation timestamp:"
                 << update_conv_query.lastError().text();
    }
  }

  return {};
}

std::expected<SettingsPtr, std::error_code>
DatabaseService::getSettings() const {

  QSqlQuery query(db_);
  query.prepare("SELECT key, value FROM settings");

  if (!query.exec()) {
    qWarning() << "Failed to get settings:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  SettingsPtr settings = std::make_unique<QVariantMap>();
  while (query.next()) {
    QString key_str = query.value(0).toString();
    QString value_str = query.value(1).toString();

    // Try to parse as JSON
    QJsonDocument doc = QJsonDocument::fromJson(value_str.toUtf8());
    if (!doc.isNull()) {
      if (doc.isObject()) {
        (*settings)[key_str] = doc.object().toVariantMap();
      } else {
        (*settings)[key_str] = doc.toVariant();
      }
    } else {
      (*settings)[key_str] = value_str;
    }
  }

  return settings;
}

std::error_code DatabaseService::saveSetting(const QString &key,
                                             const QVariant &value) {

  // Convert value to JSON string
  QString value_str;
  if (value.typeId() == QMetaType::QVariantMap ||
      value.typeId() == QMetaType::QVariantList) {
    QJsonDocument doc = QJsonDocument::fromVariant(value);
    value_str = QString::fromUtf8(doc.toJson(QJsonDocument::Compact));
  } else {
    value_str = value.toString();
  }

  // clang-format off
  using ST = SettingFields;
  constexpr static std::string_view upsert_sql{
    "INSERT INTO settings (" + ST::all_fields() +
    ") VALUES (?, ?, ?) ON CONFLICT(key) DO UPDATE SET " +
    ST::value      + "=excluded." + ST::value      + "," +
    ST::updated_at + "=excluded." + ST::updated_at
  };
  // clang-format on

  QSqlQuery upsert_query{db_};
  upsert_query.prepare(QString::fromStdString(std::string{upsert_sql}));

  QString update_time = QDateTime::currentDateTime().toString(Qt::ISODate);
  upsert_query.addBindValue(key);
  upsert_query.addBindValue(value_str);
  upsert_query.addBindValue(update_time);

  if (!upsert_query.exec()) {
    qWarning() << "Failed to save setting:" << upsert_query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::error_code DatabaseService::saveSettings(const QVariantMap &settings) {

  // Begin transaction
  db_.transaction();

  bool success = true;

  for (auto it = settings.constBegin(); it != settings.constEnd(); ++it) {
    if (!saveSetting(it.key(), it.value())) {
      success = false;
      break;
    }
  }

  // Commit or rollback transaction
  if (success) {
    if (!db_.commit()) {
      qWarning() << "Failed to commit settings:" << db_.lastError().text();
      return DatabaseError::DB_QUERY_FAILED;
    }
  } else {
    db_.rollback();
  }

  return {};
}

std::expected<QVariant, std::error_code>
DatabaseService::getSetting(const QString &key) const {

  QSqlQuery query(db_);
  query.prepare("SELECT value FROM settings WHERE key = ?");
  query.addBindValue(key);
  if (!query.exec()) {
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }
  if (!query.next()) {
    return {};
  }

  QString valueStr = query.value(0).toString();

  // Try to parse as JSON
  QJsonDocument doc = QJsonDocument::fromJson(valueStr.toUtf8());
  if (!doc.isNull()) {
    if (doc.isObject()) {
      return doc.object().toVariantMap();
    } else {
      return doc.toVariant();
    }
  }

  return valueStr;
}

std::error_code DatabaseService::deleteSetting(const QString &key) {

  QSqlQuery query(db_);
  query.prepare("DELETE FROM settings WHERE key = ?");
  query.addBindValue(key);

  if (!query.exec()) {
    qWarning() << "Failed to delete setting:" << query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::error_code DatabaseService::saveProviderKey(const QString &provider,
                                                 const QString &key) {

  // clang-format off
  using PD = ProviderFields;
  constexpr static std::string_view upsert_sql{
    "INSERT INTO provider_keys (" + PD::all_fields() +
    ") VALUES (?, ?, ?) ON CONFLICT(provider) DO UPDATE SET " +
    PD::encrypted_key + "=excluded." + PD::encrypted_key + "," +
    PD::updated_at    + "=excluded." + PD::updated_at
  };
  // clang-format on

  QSqlQuery upsert_query{db_};
  upsert_query.prepare(QString::fromStdString(std::string{upsert_sql}));

  QString encrypted_key = encryptKey(key);
  QString current_dt = QDateTime::currentDateTime().toString(Qt::ISODate);
  upsert_query.addBindValue(provider);
  upsert_query.addBindValue(encrypted_key);
  upsert_query.addBindValue(current_dt);
  if (!upsert_query.exec()) {
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::expected<QString, std::error_code>
DatabaseService::getProviderKey(const QString &provider) const {

  QSqlQuery query(db_);
  query.prepare("SELECT encrypted_key FROM provider_keys WHERE provider = ?");
  query.addBindValue(provider);

  if (!query.exec()) {
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  if (!query.next()) {
    return {};
  }

  QString encryptedKey = query.value(0).toString();
  return decryptKey(encryptedKey);
}

std::error_code DatabaseService::deleteProviderKey(const QString &provider) {
  QSqlQuery query(db_);
  query.prepare("DELETE FROM provider_keys WHERE provider = ?");
  query.addBindValue(provider);

  if (!query.exec()) {
    qWarning() << "Failed to delete provider key:" << query.lastError().text();
    return DatabaseError::DB_QUERY_FAILED;
  }

  return {};
}

std::expected<QList<ProviderPtr>, std::error_code>
DatabaseService::getAllProviderKeys() const {

  QSqlQuery query(db_);
  query.prepare("SELECT " + ProviderFields::all_fields() +
                " FROM provider_keys");

  if (!query.exec()) {
    qWarning() << "Failed to get provider keys:" << query.lastError().text();
    return std::unexpected(DatabaseError::DB_QUERY_FAILED);
  }

  QList<ProviderPtr> result{};
  while (query.next()) {
    ProviderPtr p = std::make_unique<Provider>();
    p->provider = query.value(0).toString();
    p->key = decryptKey(query.value(1).toString());
    p->update_at = query.value(2).toDateTime();
    result.append(std::move(p));
  }

  return result;
}

QString DatabaseService::encryptKey(const QString &key) const {
  /*
  // Simple encryption for demonstration purposes
  // In a real application, you would use a more secure encryption method
  // such as AES with a securely stored key

  // Generate a random salt
  QByteArray salt;
  salt.resize(16);
  for (int i = 0; i < salt.size(); ++i) {
    salt[i] = static_cast<char>(QRandomGenerator::global()->bounded(256));
  }

  // Combine salt and key
  QByteArray saltedKey = salt + key.toUtf8();

  // Hash the salted key
  QByteArray hash =
      QCryptographicHash::hash(saltedKey, QCryptographicHash::Sha256);

  // Return salt + hash as base64
  return QString::fromLatin1(salt.toBase64() + ":" + hash.toBase64());
  */
  return key;
}

QString DatabaseService::decryptKey(const QString &encryptedKey) const {
  // This is not actual decryption since we're using a hash function
  // In a real application, you would use proper encryption/decryption

  // For demonstration purposes, we'll just return a placeholder
  // In a real app, you would decrypt using the same key used for encryption
  return encryptedKey;

  // Note: In a real application, you would:
  // 1. Use a secure encryption algorithm like AES
  // 2. Store the encryption key securely (e.g., in the system keychain)
  // 3. Properly decrypt the stored API keys when needed
}

std::error_code DatabaseService::createTables() {
  QStringList tables = {
      // Conversations table
      "CREATE TABLE IF NOT EXISTS conversations ("
      "id TEXT PRIMARY KEY,"
      "title TEXT NOT NULL,"
      "created_at TEXT NOT NULL,"
      "updated_at TEXT NOT NULL,"
      "provider TEXT,"
      "model TEXT,"
      "system_prompt TEXT,"
      "temperature REAL DEFAULT 0.7,"
      "max_tokens INTEGER DEFAULT 2000,"
      "is_template INTEGER DEFAULT 0,"
      "template_name TEXT,"
      "settings TEXT"
      ")",

      // Messages table
      "CREATE TABLE IF NOT EXISTS messages ("
      "id TEXT PRIMARY KEY,"
      "conversation_id TEXT NOT NULL,"
      "content TEXT NOT NULL,"
      "role TEXT NOT NULL,"
      "timestamp TEXT NOT NULL,"
      "metadata TEXT,"
      "FOREIGN KEY (conversation_id) REFERENCES conversations (id) ON DELETE "
      "CASCADE"
      ")",

      // Attachments table
      "CREATE TABLE IF NOT EXISTS attachments ("
      "id TEXT PRIMARY KEY,"
      "message_id TEXT NOT NULL,"
      "conversation_id TEXT NOT NULL,"
      "filename TEXT NOT NULL,"
      "path TEXT NOT NULL,"
      "type TEXT NOT NULL,"
      "mime_type TEXT,"
      "size INTEGER,"
      "FOREIGN KEY (message_id) REFERENCES messages (id) ON DELETE CASCADE"
      ")",

      // Settings table
      "CREATE TABLE IF NOT EXISTS settings ("
      "key TEXT PRIMARY KEY,"
      "value TEXT NOT NULL,"
      "updated_at TEXT"
      ")",

      // Provider keys table
      "CREATE TABLE IF NOT EXISTS provider_keys ("
      "provider TEXT PRIMARY KEY,"
      "encrypted_key TEXT NOT NULL,"
      "updated_at TEXT NOT NULL"
      ")",

      // Create indexes for better performance
      "CREATE INDEX IF NOT EXISTS idx_conversations_updated_at ON "
      "conversations (updated_at)",
      "CREATE INDEX IF NOT EXISTS idx_conversations_is_template ON "
      "conversations (is_template)",
      "CREATE INDEX IF NOT EXISTS idx_messages_conversation_id ON messages "
      "(conversation_id)",
      "CREATE INDEX IF NOT EXISTS idx_messages_timestamp ON messages "
      "(timestamp)",
      "CREATE INDEX IF NOT EXISTS idx_attachments_message_id ON attachments "
      "(message_id)",
      "CREATE INDEX IF NOT EXISTS idx_attachments_conversation_id ON "
      "attachments "
      "(conversation_id)",
  };

  for (const QString &sql : tables) {
    QSqlQuery query(db_);
    if (!query.exec(sql)) {
      qWarning() << "Failed to create table or index:"
                 << query.lastError().text();
      return DatabaseError::DB_QUERY_FAILED;
    }
  }

  return {};
}

std::error_code DatabaseService::openDatabase() {
  // Get data directory
  QDir dataDir(
      QStandardPaths::writableLocation(QStandardPaths::AppDataLocation));

  if (!dataDir.exists() && !dataDir.mkpath(".")) {
    qWarning() << "Failed to create data directory:" << dataDir.path();
    return DatabaseError::DB_DIR_CREATE_FAILED;
  }

  // Set up database with a unique connection name
  db_ = QSqlDatabase::addDatabase("QSQLITE", "chamberui_connection");
  db_.setDatabaseName(dataDir.filePath("chamberui.db"));

  if (!db_.open()) {
    qWarning() << "Failed to open database:" << db_.lastError().text();
    return DatabaseError::DB_OPEN_FAILED;
  }

  return {};
}

void DatabaseService::closeDatabase() {
  if (db_.isOpen()) {
    db_.close();
  }

  // Remove the database connection
  QSqlDatabase::removeDatabase("chamberui_connection");
}

} // namespace ChamberUI::Services