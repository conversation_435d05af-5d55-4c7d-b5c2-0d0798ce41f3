#ifndef UPDATESERVICE_H
#define UPDATESERVICE_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QNetworkAccessManager>
#include "chamberservices_global.h"

#include <QNetworkReply>
#include "chamberservices_global.h"

#include <QJsonDocument>
#include "chamberservices_global.h"

#include <QJsonObject>
#include "chamberservices_global.h"

#include <QTimer>
#include <QVersionNumber>

class CHAMBERSERVICES_EXPORT UpdateService : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool updateAvailable READ updateAvailable NOTIFY updateAvailableChanged)
    Q_PROPERTY(QString latestVersion READ latestVersion NOTIFY latestVersionChanged)
    Q_PROPERTY(QString releaseNotes READ releaseNotes NOTIFY releaseNotesChanged)
    Q_PROPERTY(QString downloadUrl READ downloadUrl NOTIFY downloadUrlChanged)
    Q_PROPERTY(bool checkingForUpdates READ checkingForUpdates NOTIFY checkingForUpdatesChanged)

public:
    explicit UpdateService(QObject *parent = nullptr);
    
    bool updateAvailable() const;
    QString latestVersion() const;
    QString releaseNotes() const;
    QString downloadUrl() const;
    bool checkingForUpdates() const;
    
    Q_INVOKABLE void checkForUpdates();
    Q_INVOKABLE void downloadUpdate();
    Q_INVOKABLE void skipUpdate();
    Q_INVOKABLE void remindLater();
    
    // Set the current version for comparison
    void setCurrentVersion(const QString &version);
    
    // Set the update check URL
    void setUpdateUrl(const QString &url);
    
    // Set how often to check for updates (in hours)
    void setCheckInterval(int hours);
    
    // Enable/disable automatic update checks
    void setAutoCheckEnabled(bool enabled);

signals:
    void updateAvailableChanged();
    void latestVersionChanged();
    void releaseNotesChanged();
    void downloadUrlChanged();
    void checkingForUpdatesChanged();
    void updateCheckCompleted(bool updateAvailable);
    void updateDownloadStarted();
    void updateDownloadProgress(qint64 bytesReceived, qint64 bytesTotal);
    void updateDownloadCompleted(const QString &filePath);
    void updateDownloadFailed(const QString &error);

private slots:
    void handleNetworkReply(QNetworkReply *reply);
    void performAutoUpdateCheck();

private:
    QNetworkAccessManager *m_networkManager;
    QTimer *m_updateCheckTimer;
    
    bool m_updateAvailable;
    QString m_currentVersion;
    QString m_latestVersion;
    QString m_releaseNotes;
    QString m_downloadUrl;
    QString m_updateUrl;
    bool m_checkingForUpdates;
    bool m_autoCheckEnabled;
    
    bool compareVersions(const QString &currentVersion, const QString &latestVersion);
    void parseUpdateInfo(const QJsonObject &updateInfo);
    QString getDownloadDirectory() const;
};

#endif // UPDATESERVICE_H