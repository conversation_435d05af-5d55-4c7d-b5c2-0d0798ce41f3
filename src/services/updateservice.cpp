#include "updateservice.h"
#include <QNetworkRequest>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QDesktopServices>
#include <QUrl>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDebug>
#include <QCoreApplication>

UpdateService::UpdateService(QObject *parent)
    : QObject(parent)
    , m_networkManager(new QNetworkAccessManager(this))
    , m_updateCheckTimer(new QTimer(this))
    , m_updateAvailable(false)
    , m_currentVersion(QCoreApplication::applicationVersion())
    , m_latestVersion("")
    , m_releaseNotes("")
    , m_downloadUrl("")
    , m_updateUrl("https://soloholic.com/api/chamberui/updates")
    , m_checkingForUpdates(false)
    , m_autoCheckEnabled(true)
{
    // Connect network manager signals
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &UpdateService::handleNetworkReply);

    // Set up timer for automatic update checks
    m_updateCheckTimer->setInterval(24 * 60 * 60 * 1000); // Default: check once a day
    connect(m_updateCheckTimer, &QTimer::timeout, this, &UpdateService::performAutoUpdateCheck);

    // Start the timer if auto-check is enabled
    if (m_autoCheckEnabled) {
        m_updateCheckTimer->start();

        // Perform an initial check after a short delay
        QTimer::singleShot(5000, this, &UpdateService::performAutoUpdateCheck);
    }
}

bool UpdateService::updateAvailable() const
{
    return m_updateAvailable;
}

QString UpdateService::latestVersion() const
{
    return m_latestVersion;
}

QString UpdateService::releaseNotes() const
{
    return m_releaseNotes;
}

QString UpdateService::downloadUrl() const
{
    return m_downloadUrl;
}

bool UpdateService::checkingForUpdates() const
{
    return m_checkingForUpdates;
}

void UpdateService::checkForUpdates()
{
    if (m_checkingForUpdates) {
        return;
    }

    m_checkingForUpdates = true;
    emit checkingForUpdatesChanged();

    QNetworkRequest request;
    request.setUrl(QUrl(m_updateUrl));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // Add current version and platform info to the request
    QJsonObject requestData;
    requestData["currentVersion"] = m_currentVersion;
    requestData["platform"] = "macOS";
    requestData["arch"] = "x86_64"; // Or "arm64" for Apple Silicon

    QNetworkReply *reply = m_networkManager->post(request, QJsonDocument(requestData).toJson());

    // Connect to the progress signal for this specific reply
    connect(reply, &QNetworkReply::downloadProgress, this, [](qint64 bytesReceived, qint64 bytesTotal) {
        if (bytesTotal > 0) {
            qDebug() << "Update check progress:" << (bytesReceived * 100 / bytesTotal) << "%";
        }
    });
}

void UpdateService::downloadUpdate()
{
    if (!m_updateAvailable || m_downloadUrl.isEmpty()) {
        emit updateDownloadFailed("No update available to download");
        return;
    }

    emit updateDownloadStarted();

    QNetworkRequest request;
    request.setUrl(QUrl(m_downloadUrl));
    QNetworkReply *reply = m_networkManager->get(request);

    // Connect to the progress signal for this specific reply
    connect(reply, &QNetworkReply::downloadProgress, this, [this](qint64 bytesReceived, qint64 bytesTotal) {
        emit updateDownloadProgress(bytesReceived, bytesTotal);
    });
}

void UpdateService::skipUpdate()
{
    // User has chosen to skip this update
    // We could store the skipped version in settings
    m_updateAvailable = false;
    emit updateAvailableChanged();
}

void UpdateService::remindLater()
{
    // User wants to be reminded later
    // We could reset the timer to check again sooner
    QTimer::singleShot(4 * 60 * 60 * 1000, this, &UpdateService::checkForUpdates); // Remind in 4 hours
}

void UpdateService::setCurrentVersion(const QString &version)
{
    if (m_currentVersion != version) {
        m_currentVersion = version;
    }
}

void UpdateService::setUpdateUrl(const QString &url)
{
    if (m_updateUrl != url) {
        m_updateUrl = url;
    }
}

void UpdateService::setCheckInterval(int hours)
{
    if (hours > 0) {
        m_updateCheckTimer->setInterval(hours * 60 * 60 * 1000);

        // Restart the timer if it's running
        if (m_updateCheckTimer->isActive()) {
            m_updateCheckTimer->start();
        }
    }
}

void UpdateService::setAutoCheckEnabled(bool enabled)
{
    if (m_autoCheckEnabled != enabled) {
        m_autoCheckEnabled = enabled;

        if (m_autoCheckEnabled) {
            m_updateCheckTimer->start();
        } else {
            m_updateCheckTimer->stop();
        }
    }
}

void UpdateService::handleNetworkReply(QNetworkReply *reply)
{
    if (m_checkingForUpdates) {
        m_checkingForUpdates = false;
        emit checkingForUpdatesChanged();
    }

    if (reply->error() == QNetworkReply::NoError) {
        // Check if this is an update check response or a download
        if (reply->url().toString().startsWith(m_updateUrl)) {
            // This is an update check response
            QByteArray data = reply->readAll();
            QJsonDocument doc = QJsonDocument::fromJson(data);

            if (doc.isObject()) {
                parseUpdateInfo(doc.object());
            } else {
                qWarning() << "Invalid update info format";
                emit updateCheckCompleted(false);
            }
        } else {
            // This is a download response
            QString fileName = "ChamberUI-" + m_latestVersion + ".dmg";
            QString filePath = getDownloadDirectory() + "/" + fileName;

            QFile file(filePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(reply->readAll());
                file.close();

                emit updateDownloadCompleted(filePath);

                // Open the downloaded file
                QDesktopServices::openUrl(QUrl::fromLocalFile(filePath));
            } else {
                emit updateDownloadFailed("Failed to save the downloaded file");
            }
        }
    } else {
        if (reply->url().toString().startsWith(m_updateUrl)) {
            qWarning() << "Update check failed:" << reply->errorString();
            emit updateCheckCompleted(false);
        } else {
            emit updateDownloadFailed(reply->errorString());
        }
    }

    reply->deleteLater();
}

void UpdateService::performAutoUpdateCheck()
{
    if (m_autoCheckEnabled && !m_checkingForUpdates) {
        checkForUpdates();
    }
}

bool UpdateService::compareVersions(const QString &currentVersion, const QString &latestVersion)
{
    QVersionNumber current = QVersionNumber::fromString(currentVersion);
    QVersionNumber latest = QVersionNumber::fromString(latestVersion);

    return latest > current;
}

void UpdateService::parseUpdateInfo(const QJsonObject &updateInfo)
{
    if (updateInfo.contains("version") && updateInfo.contains("downloadUrl")) {
        QString newVersion = updateInfo["version"].toString();

        // Check if this is a newer version
        if (compareVersions(m_currentVersion, newVersion)) {
            m_latestVersion = newVersion;
            emit latestVersionChanged();

            m_downloadUrl = updateInfo["downloadUrl"].toString();
            emit downloadUrlChanged();

            if (updateInfo.contains("releaseNotes")) {
                m_releaseNotes = updateInfo["releaseNotes"].toString();
            } else {
                m_releaseNotes = "New version " + newVersion + " is available.";
            }
            emit releaseNotesChanged();

            m_updateAvailable = true;
            emit updateAvailableChanged();
            emit updateCheckCompleted(true);
        } else {
            m_updateAvailable = false;
            emit updateAvailableChanged();
            emit updateCheckCompleted(false);
        }
    } else {
        qWarning() << "Update info missing required fields";
        emit updateCheckCompleted(false);
    }
}

QString UpdateService::getDownloadDirectory() const
{
    QString downloadDir = QStandardPaths::writableLocation(QStandardPaths::DownloadLocation);

    // Create the directory if it doesn't exist
    QDir dir(downloadDir);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    return downloadDir;
}