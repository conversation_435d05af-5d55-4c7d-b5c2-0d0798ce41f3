#include "providermanager.h"
#include "openai_provider.h"
#include "anthropic_provider.h"
#include <QTimer>
#include <QRandomGenerator>
#include <QDebug>

// Placeholder provider implementation
class ProviderManager::PlaceholderProvider : public Provider
{
public:
    explicit PlaceholderProvider(QObject *parent = nullptr)
        : Provider(parent)
        , m_settings({{"model", "placeholder"}})
    {}

    QString name() const override { return "placeholder"; }
    QString displayName() const override { return "Placeholder Provider"; }
    bool requiresApiKey() const override { return false; }

    void setSettings(const QVariantMap &settings) override { m_settings = settings; }
    QVariantMap settings() const override { return m_settings; }

    QStringList availableModels() const override {
        return QStringList() << "placeholder-model";
    }

    void processMessage(const QString &conversationId,
                       const QVariantList &history,
                       const QString &message,
                       const QVariantMap &conversationSettings = QVariantMap()) override {
        emit processingStarted(conversationId);

        // Simulate processing delay (1-3 seconds)
        int delay = QRandomGenerator::global()->bounded(1000, 3000);

        QTimer::singleShot(delay, this, [this, conversationId, message]() {
            // Generate a simple response based on the message
            QString response;

            if (message.contains("hello", Qt::CaseInsensitive) ||
                message.contains("hi", Qt::CaseInsensitive)) {
                response = "Hello! How can I assist you today?";
            }
            else if (message.contains("help", Qt::CaseInsensitive)) {
                response = "I'm here to help. What do you need assistance with?";
            }
            else if (message.contains("thanks", Qt::CaseInsensitive) ||
                    message.contains("thank you", Qt::CaseInsensitive)) {
                response = "You're welcome! Is there anything else you'd like to know?";
            }
            else if (message.contains("bye", Qt::CaseInsensitive) ||
                    message.contains("goodbye", Qt::CaseInsensitive)) {
                response = "Goodbye! Feel free to chat again if you have more questions.";
            }
            else if (message.contains("?")) {
                response = "That's an interesting question. In a fully implemented version, I would provide a detailed answer based on my knowledge.";
            }
            else {
                response = "This is a placeholder response. In the actual implementation, this would be a response from a real LLM provider.";
            }

            emit responseReceived(conversationId, response);
            emit processingFinished(conversationId);
        });
    }

private:
    QVariantMap m_settings;
};

ProviderManager::ProviderManager(QObject *parent)
    : QObject(parent)
    , m_currentProviderName("placeholder")
    , m_databaseService(DatabaseService::instance(this))
{
    // Initialize database
    if (!m_databaseService->initialize()) {
        qWarning() << "Failed to initialize database service";
    }

    // Create placeholder provider
    m_placeholderProvider = std::make_unique<PlaceholderProvider>(this);
    registerProvider(m_placeholderProvider.get());

    // Create and register real providers
    registerProvider(new OpenAIProvider(this));
    registerProvider(new AnthropicProvider(this));

    // Load provider keys from database
    loadProviderKeys();

    // Load current provider from settings
    QVariant currentProvider = m_databaseService->getSetting("currentProvider");
    if (currentProvider.isValid() && hasProvider(currentProvider.toString())) {
        setCurrentProvider(currentProvider.toString());
    } else if (hasProvider("openai")) {
        setCurrentProvider("openai");
    }
}

QVariantList ProviderManager::availableProviders() const
{
    QVariantList result;

    for (auto it = m_providers.constBegin(); it != m_providers.constEnd(); ++it) {
        Provider *provider = it.value();

        QVariantMap providerInfo;
        providerInfo["name"] = provider->name();
        providerInfo["displayName"] = provider->displayName();
        providerInfo["requiresApiKey"] = provider->requiresApiKey();

        result.append(providerInfo);
    }

    return result;
}

bool ProviderManager::hasProvider(const QString &name) const
{
    return m_providers.contains(name);
}

void ProviderManager::registerProvider(Provider *provider)
{
    if (!provider) {
        return;
    }

    QString name = provider->name();

    if (m_providers.contains(name)) {
        // Provider already registered
        return;
    }

    // Connect provider signals
    connect(provider, &Provider::responseReceived, this, &ProviderManager::responseReceived);
    connect(provider, &Provider::errorOccurred, this, &ProviderManager::errorOccurred);
    connect(provider, &Provider::processingStarted, this, &ProviderManager::processingStarted);
    connect(provider, &Provider::processingFinished, this, &ProviderManager::processingFinished);

    // Register provider
    m_providers[name] = provider;

    emit providerRegistered(name);
}

Provider* ProviderManager::currentProvider() const
{
    if (!m_providers.contains(m_currentProviderName)) {
        // Return placeholder provider if current provider is not available
        return m_placeholderProvider.get();
    }

    return m_providers[m_currentProviderName];
}

QString ProviderManager::currentProviderName() const
{
    return m_currentProviderName;
}

void ProviderManager::setCurrentProvider(const QString &name)
{
    if (m_currentProviderName == name) {
        return;
    }

    if (!m_providers.contains(name)) {
        // Provider not found, use placeholder
        m_currentProviderName = "placeholder";
    } else {
        m_currentProviderName = name;
    }

    // Save current provider to settings
    m_databaseService->saveSetting("currentProvider", m_currentProviderName);

    emit currentProviderChanged(m_currentProviderName);
}

void ProviderManager::setProviderSettings(const QString &name, const QVariantMap &settings)
{
    if (!m_providers.contains(name)) {
        return;
    }

    // Save API key to database if provided
    if (settings.contains("apiKey")) {
        saveProviderKey(name, settings["apiKey"].toString());
    }

    m_providers[name]->setSettings(settings);
}

QVariantMap ProviderManager::providerSettings(const QString &name) const
{
    if (!m_providers.contains(name)) {
        return QVariantMap();
    }

    return m_providers[name]->settings();
}

void ProviderManager::processMessage(const QString &conversationId,
                                    const QVariantList &history,
                                    const QString &message,
                                    const QVariantMap &conversationSettings)
{
    Provider *provider = currentProvider();
    if (provider) {
        provider->processMessage(conversationId, history, message, conversationSettings);
    }
}

void ProviderManager::loadProviderKeys()
{
    // Get all provider keys from database
    QVariantList providerKeys = m_databaseService->getAllProviderKeys();

    for (const QVariant &providerKeyVar : providerKeys) {
        QVariantMap providerKeyMap = providerKeyVar.toMap();
        QString providerName = providerKeyMap["provider"].toString();

        if (hasProvider(providerName)) {
            // Get the actual key
            QString apiKey = m_databaseService->getProviderKey(providerName);
            if (!apiKey.isEmpty()) {
                // Update provider settings with the API key
                QVariantMap settings = providerSettings(providerName);
                settings["apiKey"] = apiKey;
                m_providers[providerName]->setSettings(settings);
            }
        }
    }
}

void ProviderManager::saveProviderKey(const QString &provider, const QString &key)
{
    if (!m_databaseService->saveProviderKey(provider, key)) {
        qWarning() << "Failed to save API key for provider:" << provider;
    }
}