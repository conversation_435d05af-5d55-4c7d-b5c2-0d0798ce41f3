#include "openai_provider.h"
#include <QDebug>
#include <QUrlQuery>
#include <QNetworkRequest>

OpenAIProvider::OpenAIProvider(QObject *parent)
    : Provider(parent)
    , m_networkManager(new QNetworkAccessManager(this))
{
    // Initialize default settings
    m_settings["apiKey"] = "";
    m_settings["model"] = "gpt-4o";
    m_settings["temperature"] = 0.7;
    m_settings["maxTokens"] = 2000;
    
    // Connect network manager signals
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &OpenAIProvider::handleNetworkReply);
}

OpenAIProvider::~OpenAIProvider()
{
    // Cancel any pending requests
    for (auto reply : m_activeRequests.keys()) {
        reply->abort();
    }
}

QString OpenAIProvider::name() const
{
    return "openai";
}

QString OpenAIProvider::displayName() const
{
    return "OpenAI";
}

bool OpenAIProvider::requiresApiKey() const
{
    return true;
}

void OpenAIProvider::setSettings(const QVariantMap &settings)
{
    // Merge new settings with existing ones
    for (auto it = settings.constBegin(); it != settings.constEnd(); ++it) {
        m_settings[it.key()] = it.value();
    }
}

QVariantMap OpenAIProvider::settings() const
{
    return m_settings;
}

QStringList OpenAIProvider::availableModels() const
{
    return QStringList() 
        << "gpt-4o" 
        << "gpt-4-turbo" 
        << "gpt-4" 
        << "gpt-3.5-turbo";
}

void OpenAIProvider::processMessage(const QString &conversationId,
                                   const QVariantList &history,
                                   const QString &message,
                                   const QVariantMap &conversationSettings)
{
    // Check if API key is set
    if (m_settings["apiKey"].toString().isEmpty()) {
        emit errorOccurred(conversationId, "API key is not set. Please set your OpenAI API key in the settings.");
        return;
    }
    
    emit processingStarted(conversationId);
    
    // Prepare the request
    QNetworkRequest request(QUrl("https://api.openai.com/v1/chat/completions"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("Authorization", QString("Bearer %1").arg(m_settings["apiKey"].toString()).toUtf8());
    
    // Prepare the request body
    QJsonObject requestObj;
    
    // Use conversation-specific model if provided, otherwise use the default
    if (conversationSettings.contains("model") && !conversationSettings["model"].toString().isEmpty()) {
        requestObj["model"] = conversationSettings["model"].toString();
    } else {
        requestObj["model"] = m_settings["model"].toString();
    }
    
    // Use conversation-specific system prompt if provided
    QVariantList historyWithSystemPrompt = history;
    if (conversationSettings.contains("systemPrompt") && !conversationSettings["systemPrompt"].toString().isEmpty()) {
        // Remove any existing system messages
        for (int i = historyWithSystemPrompt.size() - 1; i >= 0; i--) {
            QVariantMap msg = historyWithSystemPrompt[i].toMap();
            if (msg["role"].toString() == "system") {
                historyWithSystemPrompt.removeAt(i);
            }
        }
        
        // Add the conversation-specific system prompt
        QVariantMap systemMsg;
        systemMsg["role"] = "system";
        systemMsg["content"] = conversationSettings["systemPrompt"].toString();
        historyWithSystemPrompt.prepend(systemMsg);
    }
    
    requestObj["messages"] = formatMessages(historyWithSystemPrompt, message);
    
    // Use conversation-specific temperature if provided, otherwise use the default
    if (conversationSettings.contains("temperature")) {
        requestObj["temperature"] = conversationSettings["temperature"].toDouble();
    } else {
        requestObj["temperature"] = m_settings["temperature"].toDouble();
    }
    
    // Use conversation-specific max tokens if provided, otherwise use the default
    if (conversationSettings.contains("maxTokens") && conversationSettings["maxTokens"].toInt() > 0) {
        requestObj["max_tokens"] = conversationSettings["maxTokens"].toInt();
    } else if (m_settings.contains("maxTokens") && m_settings["maxTokens"].toInt() > 0) {
        requestObj["max_tokens"] = m_settings["maxTokens"].toInt();
    }
    
    // Add any additional settings from the conversation
    if (conversationSettings.contains("settings")) {
        QVariantMap additionalSettings = conversationSettings["settings"].toMap();
        for (auto it = additionalSettings.constBegin(); it != additionalSettings.constEnd(); ++it) {
            // Skip settings we've already handled
            if (it.key() != "model" && it.key() != "temperature" && it.key() != "maxTokens" && it.key() != "systemPrompt") {
                requestObj[it.key()] = QJsonValue::fromVariant(it.value());
            }
        }
    }
    
    QJsonDocument requestDoc(requestObj);
    QByteArray requestData = requestDoc.toJson();
    
    // Send the request
    QNetworkReply *reply = m_networkManager->post(request, requestData);
    m_activeRequests[reply] = conversationId;
    
    // Debug output
    qDebug() << "OpenAI request sent for conversation:" << conversationId;
}

void OpenAIProvider::handleNetworkReply(QNetworkReply *reply)
{
    // Get the conversation ID for this reply
    QString conversationId = m_activeRequests.value(reply);
    
    // Remove from active requests
    m_activeRequests.remove(reply);
    
    // Handle the reply
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray responseData = reply->readAll();
        QJsonDocument responseDoc = QJsonDocument::fromJson(responseData);
        QJsonObject responseObj = responseDoc.object();
        
        // Extract the response content
        QString content = extractContent(responseObj);
        
        if (!content.isEmpty()) {
            emit responseReceived(conversationId, content);
        } else {
            emit errorOccurred(conversationId, "Failed to extract content from OpenAI response");
        }
    } else {
        // Handle error
        QByteArray responseData = reply->readAll();
        QString errorMessage;
        
        // Try to parse error message from JSON response
        QJsonDocument errorDoc = QJsonDocument::fromJson(responseData);
        if (!errorDoc.isNull() && errorDoc.isObject()) {
            QJsonObject errorObj = errorDoc.object();
            if (errorObj.contains("error") && errorObj["error"].isObject()) {
                QJsonObject error = errorObj["error"].toObject();
                if (error.contains("message")) {
                    errorMessage = error["message"].toString();
                }
            }
        }
        
        // If we couldn't parse the error message, use the reply's error string
        if (errorMessage.isEmpty()) {
            errorMessage = reply->errorString();
        }
        
        emit errorOccurred(conversationId, QString("OpenAI API error: %1").arg(errorMessage));
    }
    
    // Signal that processing is finished
    emit processingFinished(conversationId);
    
    // Clean up
    reply->deleteLater();
}

QJsonArray OpenAIProvider::formatMessages(const QVariantList &history, const QString &message)
{
    QJsonArray messages;
    
    // Add system message if not present in history
    bool hasSystemMessage = false;
    
    // Add history messages
    for (const QVariant &item : history) {
        QVariantMap messageItem = item.toMap();
        QString role = messageItem["role"].toString();
        
        if (role == "system") {
            hasSystemMessage = true;
        }
        
        QJsonObject messageObj;
        messageObj["role"] = role;
        messageObj["content"] = messageItem["content"].toString();
        messages.append(messageObj);
    }
    
    // Add default system message if none exists
    if (!hasSystemMessage) {
        QJsonObject systemMessage;
        systemMessage["role"] = "system";
        systemMessage["content"] = "You are a helpful assistant.";
        messages.prepend(systemMessage);
    }
    
    // Add the current user message
    QJsonObject userMessage;
    userMessage["role"] = "user";
    userMessage["content"] = message;
    messages.append(userMessage);
    
    return messages;
}

QString OpenAIProvider::extractContent(const QJsonObject &responseObj)
{
    if (responseObj.contains("choices") && responseObj["choices"].isArray()) {
        QJsonArray choices = responseObj["choices"].toArray();
        
        if (!choices.isEmpty() && choices[0].isObject()) {
            QJsonObject choice = choices[0].toObject();
            
            if (choice.contains("message") && choice["message"].isObject()) {
                QJsonObject message = choice["message"].toObject();
                
                if (message.contains("content")) {
                    return message["content"].toString();
                }
            }
        }
    }
    
    return QString();
}