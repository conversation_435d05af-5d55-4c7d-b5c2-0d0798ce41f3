#pragma once

#include <expected>
#include <gsl/gsl>

#include <QDateTime>
#include <QList>
#include <QSqlDatabase>
#include <QString>
#include <QVariantList>
#include <QVariantMap>

#include "models/Conversation.h"
#include "models/Message.h"
#include "models/provider.h"

namespace ChamberUI::Bootstrap {
class BootstrapManager;
}

namespace ChamberUI::Services {

using namespace Models;
using SettingsPtr = std::unique_ptr<QVariantMap>;

class DatabaseService : public QObject {
  Q_OBJECT

public:
  static std::expected<gsl::not_null<DatabaseService *>, std::error_code>
  createDatabaseService(gsl::not_null<QObject *> *parent);
  // Delete copy constructor and assignment operator
  DatabaseService(const DatabaseService &) = delete;
  ~DatabaseService() final;

  DatabaseService &operator=(const DatabaseService &) = delete;

  // Conversation methods
  [[nodiscard]]
  std::expected<ConversationListPtr, std::error_code>
  getAllConversations() const;
  [[nodiscard]]
  std::expected<ConversationPtr, std::error_code>
  getConversation(const QString &id) const;
  [[nodiscard]]
  std::expected<MessageListPtr, std::error_code>
  getConversationMessages(const QString &id) const;
  [[nodiscard]]
  std::error_code saveConversation(const Conversation &conv);
  [[nodiscard]]
  std::error_code deleteConversation(const QString &id);
  [[nodiscard]]
  std::error_code clearAllConversations();

  // Message methods
  std::error_code saveMessages(const QList<MessagePtr> &messages);
  std::error_code deleteMessage(const QString &convsation_id,
                                const QString &message_id);
  std::error_code deleteMessages(const QString &conversation_id);

  // Attachment methods
  [[nodiscard]] std::expected<AttachmentListPtr, std::error_code>
  getMessageAttachments(const QString &message_id) const;
  [[nodiscard]]
  std::error_code saveAttachments(const QList<AttachmentPtr> &attachments,
                                  bool in_transaction = true);

  std::error_code deleteAttachment(const QString &attachment_id);
  std::error_code deleteAttachments(const QString &message_id);

  // Settings methods
  [[nodiscard]]
  std::expected<SettingsPtr, std::error_code> getSettings() const;
  std::error_code saveSetting(const QString &key, const QVariant &value);
  std::error_code saveSettings(const QVariantMap &settings);

  [[nodiscard]]
  std::expected<QVariant, std::error_code> getSetting(const QString &key) const;
  std::error_code deleteSetting(const QString &key);

  // Provider key methods
  std::error_code saveProviderKey(const QString &provider, const QString &key);

  [[nodiscard]]
  std::expected<QString, std::error_code>
  getProviderKey(const QString &provider) const;

  std::error_code deleteProviderKey(const QString &provider);
  [[nodiscard]]
  std::expected<QList<ProviderPtr>, std::error_code> getAllProviderKeys() const;

signals:
  void databaseInitialized();
  void databaseError(const QString &error);

private:
  friend class Bootstrap::BootstrapManager;
  // Private constructor for singleton pattern
  explicit DatabaseService(gsl::not_null<QObject *> parent);

  std::error_code initialize();

  [[nodiscard]] std::error_code createTables();
  [[nodiscard]] std::error_code openDatabase();
  void closeDatabase();
  // Encryption helpers for API keys
  QString encryptKey(const QString &key) const;
  QString decryptKey(const QString &encryptedKey) const;

  QSqlDatabase db_;
  std::error_code init_ec_;
};

} // namespace ChamberUI::Services
