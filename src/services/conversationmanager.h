#pragma once

#include "Conversation.h"
#include "chamberservices_global.h"

#include <QList>
#include <QString>
#include <QVariantList>

#include "../backup/conversation.h"
#include "../backup/message.h"

namespace ChamberUI::Services {
class DatabaseService;

class CHAMBERSERVICES_EXPORT ConversationManager : public QObject {
  Q_OBJECT

public:
  explicit ConversationManager(DatabaseService *database_service,
                               QObject *parent = nullptr);

  uint32_t getConversationCount() const;
  QList<const Models::Conversation *> getConversations() const;
  const Models::Conversation &getConversation(const QString &id) const;
  QList<const Models::Message *>
  getMessagesOfConversation(const QString &conversationId) const;

  QVariantList getConversationMessages(const QString &id) const;

  QString createConversation(const QString &title = "New Conversation");
  bool deleteConversation(const QString &id);
  bool renameConversation(const QString &id, const QString &newTitle);

  QString addMessage(const QString &conversationId, const QString &content,
                     const QString &role);
  bool deleteMessage(const QString &conversationId, const QString &messageId);
  bool clearConversation(const QString &conversationId);

  // Attachment methods
  QString addAttachment(const QString &conversationId, const QString &messageId,
                        const QString &filePath,
                        Attachment::Type type = Attachment::Other);
  bool removeAttachment(const QString &conversationId, const QString &messageId,
                        const QString &attachmentId);

  QVariantList getMessageAttachments(const QString &conversationId,
                                     const QString &messageId) const;

  // File handling methods
  QString saveAttachmentFile(const QString &sourceFilePath,
                             const QString &filename);
  QString getAttachmentStoragePath() const;

  // Export/Import related methods
  // QVariantMap conversationToVariantMap(const Conversation &conversation)
  // const; Conversation conversationFromVariantMap(const QVariantMap &map)
  // const;

  bool saveConversations() const;
  bool loadConversations();

signals:
  void conversationCreated(const QString &id);
  void conversationDeleted(const QString &id);
  void conversationRenamed(const QString &id, const QString &newTitle);
  void messageAdded(const QString &conversationId, const QString &messageId);
  void messageDeleted(const QString &conversationId, const QString &messageId);
  void conversationCleared(const QString &conversationId);
  void conversationsLoaded();
  void attachmentAdded(const QString &conversationId, const QString &messageId,
                       const QString &attachmentId);
  void attachmentRemoved(const QString &conversationId,
                         const QString &messageId, const QString &attachmentId);

private:
  QMap<QString, Models::Conversation> conversations_;
  DatabaseService *database_service_;

  Models::Conversation *getConversationPtr(const QString &id);
  const Models::Conversation *getConversationPtr(const QString &id) const;

  Message *getMessagePtr(Models::Conversation *conversation,
                         const QString &messageId);
  const Message *getMessagePtr(const Models::Conversation *conversation,
                               const QString &messageId) const;

  // Helper methods are already declared in public section
};

} // namespace ChamberUI::Services