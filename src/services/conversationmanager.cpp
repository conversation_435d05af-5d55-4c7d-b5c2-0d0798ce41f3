#include "conversationmanager.h"

#include <QDebug>
#include <QDir>
#include <QFileInfo>
#include <QJsonArray>
#include <QJsonObject>
#include <QMimeDatabase>
#include <QStandardPaths>
#include <QUuid>

#include "database_service.h"

namespace ChamberUI::Services {

ConversationManager::ConversationManager(DatabaseService *database_service,
                                         QObject *parent)
    : QObject(parent), database_service_(database_service) {
  // Initialize database
  if (!database_service_->initialize()) {
    qWarning() << "Failed to initialize database service";
  }

  // Create attachments directory if it doesn't exist
  QDir attachmentsDir(getAttachmentStoragePath());
  if (!attachmentsDir.exists()) {
    attachmentsDir.mkpath(".");
  }

  // Load conversations from database
  loadConversations();
}

uint32_t ConversationManager::getConversationCount() const {
  return conversations_.size();
}

QList<const Models::Conversation *>
ConversationManager::getConversations() const {
  QList<const Models::Conversation *> result;
  for (const auto &conversation : conversations_) {
    result.append(&conversation);
  }
  return result;
}

QList<const Models::Message *> ConversationManager::getMessagesOfConversation(
    const QString &conversationId) const {
  QList<const Models::Message *> result;
  database_service_->getConversation(conversationId);
}

QVariantList ConversationManager::getAllConversations() const {
  QVariantList result;

  for (const auto &conversation : m_conversations) {
    QVariantMap item;
    item["id"] = conversation.id();
    item["title"] = conversation.title();
    item["timestamp"] = conversation.updatedAt().toString(Qt::ISODate);
    item["messageCount"] = conversation.messageCount();

    result.append(item);
  }

  return result;
}

QVariantMap ConversationManager::getConversation(const QString &id) const {
  const Conversation *conversation = getConversationPtr(id);
  if (!conversation) {
    return QVariantMap();
  }

  QVariantMap result;
  result["id"] = conversation->id();
  result["title"] = conversation->title();
  result["createdAt"] = conversation->createdAt().toString(Qt::ISODate);
  result["updatedAt"] = conversation->updatedAt().toString(Qt::ISODate);
  result["messageCount"] = conversation->messageCount();

  QVariantList messages;
  for (const auto &message : conversation->messages()) {
    messages.append(message.toVariantMap());
  }

  result["messages"] = messages;

  return result;
}

QVariantList
ConversationManager::getConversationMessages(const QString &id) const {
  const Conversation *conversation = getConversationPtr(id);
  if (!conversation) {
    return QVariantList();
  }

  QVariantList messages;
  for (const auto &message : conversation->messages) {
    messages.append(message.toVariantMap());
  }

  return messages;
}

QString ConversationManager::createConversation(const QString &title) {
  QString id = QUuid::createUuid().toString(QUuid::WithoutBraces);
  Conversation conversation(id, title);

  // Set creation and update timestamps
  QDateTime now = QDateTime::currentDateTime();
  conversation.setCreatedAt(now);
  conversation.setUpdatedAt(now);

  // Add to memory cache
  m_conversations.insert(id, conversation);

  // Save to database
  QVariantMap conversationMap = conversationToVariantMap(conversation);
  if (!m_databaseService->saveConversation(conversationMap)) {
    qWarning() << "Failed to save new conversation to database";
  }

  emit conversationCreated(id);

  return id;
}

bool ConversationManager::deleteConversation(const QString &id) {
  if (!m_conversations.contains(id)) {
    return false;
  }

  // Remove from memory cache
  m_conversations.remove(id);

  // Delete from database
  if (!m_databaseService->deleteConversation(id)) {
    qWarning() << "Failed to delete conversation from database";
  }

  emit conversationDeleted(id);

  return true;
}

bool ConversationManager::renameConversation(const QString &id,
                                             const QString &newTitle) {
  Conversation *conversation = getConversationPtr(id);
  if (!conversation) {
    return false;
  }

  // Update in memory
  conversation->setTitle(newTitle);
  conversation->setUpdatedAt(QDateTime::currentDateTime());

  // Update in database
  QVariantMap conversationMap = conversationToVariantMap(*conversation);
  if (!m_databaseService->saveConversation(conversationMap)) {
    qWarning() << "Failed to update conversation title in database";
  }

  emit conversationRenamed(id, newTitle);

  return true;
}

QString ConversationManager::addMessage(const QString &conversationId,
                                        const QString &content,
                                        const QString &role) {
  Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    return QString();
  }

  // Create new message
  Message message;
  message.setContent(content);
  message.setRoleFromString(role);
  message.setTimestamp(QDateTime::currentDateTime());

  // Add to conversation in memory
  conversation->addMessage(message);
  conversation->setUpdatedAt(QDateTime::currentDateTime());

  // Save message to database
  QVariantMap messageMap = message.toVariantMap();
  if (!m_databaseService->saveMessage(conversationId, messageMap)) {
    qWarning() << "Failed to save message to database";
  }

  emit messageAdded(conversationId, message.id());

  return message.id();
}

bool ConversationManager::deleteMessage(const QString &conversationId,
                                        const QString &messageId) {
  Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    return false;
  }

  // Remove from memory
  QList<Message> messages = conversation->messages();
  for (int i = 0; i < messages.size(); ++i) {
    if (messages[i].id() == messageId) {
      messages.removeAt(i);
      conversation->setMessages(messages);
      conversation->setUpdatedAt(QDateTime::currentDateTime());

      // Delete from database
      if (!m_databaseService->deleteMessage(conversationId, messageId)) {
        qWarning() << "Failed to delete message from database";
      }

      emit messageDeleted(conversationId, messageId);

      return true;
    }
  }

  return false;
}

Message *ConversationManager::getMessagePtr(Conversation *conversation,
                                            const QString &messageId) {
  if (!conversation) {
    return nullptr;
  }

  // Get a copy of the messages list
  QList<Message> messages = conversation->messages();

  // Find the message with the matching ID
  for (int i = 0; i < messages.size(); ++i) {
    if (messages[i].id() == messageId) {
      // Note: This is returning a pointer to an element in a local copy,
      // which will be invalid when this function returns.
      // This is a design issue in the original code.
      // A better approach would be to return the index or use a different
      // design.
      return &messages[i];
    }
  }

  return nullptr;
}

const Message *
ConversationManager::getMessagePtr(const Conversation *conversation,
                                   const QString &messageId) const {
  if (!conversation) {
    return nullptr;
  }

  // Get a copy of the messages list
  QList<Message> messages = conversation->messages();

  // Find the message with the matching ID
  for (int i = 0; i < messages.size(); ++i) {
    if (messages[i].id() == messageId) {
      // Note: This is returning a pointer to an element in a local copy,
      // which will be invalid when this function returns.
      // This is a design issue in the original code.
      // A better approach would be to return the index or use a different
      // design.
      return &messages[i];
    }
  }

  return nullptr;
}

QString ConversationManager::getAttachmentStoragePath() const {
  QString appDataPath =
      QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
  return appDataPath + "/attachments";
}

QString ConversationManager::saveAttachmentFile(const QString &sourceFilePath,
                                                const QString &filename) {
  // Create a unique filename to avoid collisions
  QFileInfo fileInfo(filename);
  QString uniqueFilename = QUuid::createUuid().toString(QUuid::WithoutBraces) +
                           "." + fileInfo.suffix();

  // Destination path in the attachments directory
  QString destPath = getAttachmentStoragePath() + "/" + uniqueFilename;

  // Copy the file
  if (QFile::copy(sourceFilePath, destPath)) {
    return destPath;
  } else {
    qWarning() << "Failed to copy attachment file from" << sourceFilePath
               << "to" << destPath;
    return QString();
  }
}

QString ConversationManager::addAttachment(const QString &conversationId,
                                           const QString &messageId,
                                           const QString &filePath,
                                           Attachment::Type type) {
  Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    qWarning() << "Conversation not found:" << conversationId;
    return QString();
  }

  Message *message = getMessagePtr(conversation, messageId);
  if (!message) {
    qWarning() << "Message not found:" << messageId;
    return QString();
  }

  // Get file info
  QFileInfo fileInfo(filePath);
  if (!fileInfo.exists()) {
    qWarning() << "File does not exist:" << filePath;
    return QString();
  }

  // Determine attachment type if not specified
  if (type == Attachment::Other) {
    QMimeDatabase db;
    QString mimeType = db.mimeTypeForFile(filePath).name();

    if (mimeType.startsWith("image/")) {
      type = Attachment::Image;
    } else if (mimeType == "application/pdf") {
      type = Attachment::PDF;
    } else if (mimeType.startsWith("text/")) {
      type = Attachment::Text;
    }
  }

  // Save the file to the attachments directory
  QString savedPath = saveAttachmentFile(filePath, fileInfo.fileName());
  if (savedPath.isEmpty()) {
    qWarning() << "Failed to save attachment file";
    return QString();
  }

  // Create attachment
  Attachment attachment;
  attachment.setFilename(fileInfo.fileName());
  attachment.setPath(savedPath);
  attachment.setType(type);

  // Add to message
  message->addAttachment(attachment);

  // Save to database
  QVariantMap messageMap = message->toVariantMap();
  if (!m_databaseService->saveMessage(conversationId, messageMap)) {
    qWarning() << "Failed to save message with attachment to database";
  }

  emit attachmentAdded(conversationId, messageId, attachment.id());

  return attachment.id();
}

bool ConversationManager::removeAttachment(const QString &conversationId,
                                           const QString &messageId,
                                           const QString &attachmentId) {
  Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    return false;
  }

  Message *message = getMessagePtr(conversation, messageId);
  if (!message) {
    return false;
  }

  // Find the attachment
  QList<Attachment> attachments = message->attachments();
  for (int i = 0; i < attachments.size(); ++i) {
    if (attachments[i].id() == attachmentId) {
      // Get the file path before removing
      QString filePath = attachments[i].path();

      // Remove from message
      message->removeAttachment(attachmentId);

      // Save to database
      QVariantMap messageMap = message->toVariantMap();
      if (!m_databaseService->saveMessage(conversationId, messageMap)) {
        qWarning() << "Failed to save message after removing attachment";
      }

      // Delete the file
      QFile file(filePath);
      if (file.exists() && !file.remove()) {
        qWarning() << "Failed to delete attachment file:" << filePath;
      }

      emit attachmentRemoved(conversationId, messageId, attachmentId);

      return true;
    }
  }

  return false;
}

QVariantList
ConversationManager::getMessageAttachments(const QString &conversationId,
                                           const QString &messageId) const {
  const Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    return QVariantList();
  }

  const Message *message = getMessagePtr(conversation, messageId);
  if (!message) {
    return QVariantList();
  }

  QVariantList result;
  QList<Attachment> attachments = message->attachments();

  for (const Attachment &attachment : attachments) {
    result.append(attachment.toVariantMap());
  }

  return result;
}

bool ConversationManager::clearConversation(const QString &conversationId) {
  Conversation *conversation = getConversationPtr(conversationId);
  if (!conversation) {
    return false;
  }

  // Clear messages in memory
  conversation->clearMessages();
  conversation->setUpdatedAt(QDateTime::currentDateTime());

  // Clear messages in database
  if (!m_databaseService->clearConversationMessages(conversationId)) {
    qWarning() << "Failed to clear conversation messages in database";
  }

  emit conversationCleared(conversationId);

  return true;
}

bool ConversationManager::saveConversations() const {
  bool success = true;

  for (const auto &conversation : m_conversations) {
    QVariantMap conversationMap = conversationToVariantMap(conversation);
    if (!m_databaseService->saveConversation(conversationMap)) {
      qWarning() << "Failed to save conversation:" << conversation.id();
      success = false;
    }
  }

  return success;
}

bool ConversationManager::loadConversations() {
  // Clear existing conversations
  m_conversations.clear();

  // Get all conversations from database
  QVariantList conversationsData = m_databaseService->getAllConversations();

  for (const QVariant &conversationVar : conversationsData) {
    QVariantMap conversationMap = conversationVar.toMap();
    QString id = conversationMap["id"].toString();

    // Get full conversation with messages
    QVariantMap fullConversationMap = m_databaseService->getConversation(id);
    Conversation conversation = conversationFromVariantMap(fullConversationMap);

    m_conversations.insert(id, conversation);
  }

  emit conversationsLoaded();

  return true;
}

Conversation *ConversationManager::getConversationPtr(const QString &id) {
  auto it = m_conversations.find(id);
  if (it == m_conversations.end()) {
    return nullptr;
  }

  return &(*it);
}

const Conversation *
ConversationManager::getConversationPtr(const QString &id) const {
  auto it = m_conversations.find(id);
  if (it == m_conversations.end()) {
    return nullptr;
  }

  return &(*it);
}

QVariantMap ConversationManager::conversationToVariantMap(
    const Conversation &conversation) const {
  QVariantMap map;
  map["id"] = conversation.id.value();
  map["title"] = conversation.title.value();
  map["createdAt"] = conversation.createdAt.toString(Qt::ISODate);
  map["updatedAt"] = conversation.updatedAt.toString(Qt::ISODate);

  QVariantList messagesData;
  for (const auto &message : conversation.messages) {
    messagesData.append(message.toVariantMap());
  }
  map["messages"] = messagesData;

  return map;
}

Conversation
ConversationManager::conversationFromVariantMap(const QVariantMap &map) const {
  QString id = map["id"].toString();
  QString title = map["title"].toString();

  Conversation conversation(id, title);

  QDateTime createdAt =
      QDateTime::fromString(map["createdAt"].toString(), Qt::ISODate);
  if (createdAt.isValid()) {
    conversation.setCreatedAt(createdAt);
  }

  QDateTime updatedAt =
      QDateTime::fromString(map["updatedAt"].toString(), Qt::ISODate);
  if (updatedAt.isValid()) {
    conversation.setUpdatedAt(updatedAt);
  }

  // Set provider, model, and system prompt if available
  if (map.contains("provider")) {
    conversation.setProvider(map["provider"].toString());
  }

  if (map.contains("model")) {
    conversation.setModel(map["model"].toString());
  }

  if (map.contains("systemPrompt")) {
    conversation.setSystemPrompt(map["systemPrompt"].toString());
  }

  if (map.contains("messages")) {
    QVariantList messagesData = map["messages"].toList();
    for (const QVariant &messageVar : messagesData) {
      QVariantMap messageMap = messageVar.toMap();
      Message message = Message::fromVariantMap(messageMap);
      conversation.addMessage(message);
    }
  }

  return conversation;
}

} // namespace ChamberUI::Services
