#ifndef VOICESERVICE_H
#define VOICESERVICE_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QTextToSpeech>
#include "chamberservices_global.h"

#include <QMediaRecorder>
#include "chamberservices_global.h"

#include <QAudioInput>
#include "chamberservices_global.h"

#include <QTemporaryFile>
#include <QTimer>
#include <QSqlDatabase>

class CHAMBERSERVICES_EXPORT VoiceService : public QObject
{
    Q_OBJECT
    Q_PROPERTY(bool isRecording READ isRecording NOTIFY recordingStateChanged)
    Q_PROPERTY(bool isSpeaking READ isSpeaking NOTIFY speakingStateChanged)
    Q_PROPERTY(double recordingVolume READ recordingVolume NOTIFY recordingVolumeChanged)
    Q_PROPERTY(double speechRate READ speechRate WRITE setSpeechRate NOTIFY speechRateChanged)
    Q_PROPERTY(double pitch READ pitch WRITE setPitch NOTIFY pitchChanged)
    Q_PROPERTY(double volume READ volume WRITE setVolume NOTIFY volumeChanged)
    Q_PROPERTY(QStringList availableVoices READ availableVoices NOTIFY availableVoicesChanged)
    Q_PROPERTY(QStringList availableLanguages READ availableLanguages NOTIFY availableLanguagesChanged)

public:
    explicit VoiceService(QObject *parent = nullptr);
    ~VoiceService();

    // Speech recognition methods
    bool isRecording() const;
    double recordingVolume() const;
    QStringList availableLanguages() const;

    // Text-to-speech methods
    bool isSpeaking() const;
    QStringList availableVoices() const;

    // Settings
    QVariantMap voiceSettings() const;
    void setVoiceSettings(const QVariantMap &settings);

public slots:
    // Speech recognition
    void startRecording();
    void stopRecording();
    void cancelRecording();

    // Text-to-speech
    void speak(const QString &text);
    void pauseSpeech();
    void resumeSpeech();
    void stopSpeech();
    
    // Settings
    void setVoice(const QString &voice);
    void setSpeechRate(double rate);
    void setPitch(double pitch);
    void setVolume(double volume);
    void setLanguage(const QString &language);
    
    // Getters for properties
    double speechRate() const { return m_speechRate; }
    double pitch() const { return m_pitch; }
    double volume() const { return m_volume; }
    
    void saveSettings();
    void loadSettings();

signals:
    // Speech recognition signals
    void recordingStateChanged();
    void recordingVolumeChanged();
    void speechRecognized(const QString &text);
    void recordingError(const QString &error);
    void availableLanguagesChanged();

    // Text-to-speech signals
    void speakingStateChanged();
    void speechStarted();
    void speechPaused();
    void speechResumed();
    void speechStopped();
    void speechFinished();
    void speechError(const QString &error);
    void availableVoicesChanged();
    void speechRateChanged();
    void pitchChanged();
    void volumeChanged();

private slots:
    void handleSpeechRecognized(const QString &text);
    void updateRecordingVolume();
    void handleSpeechStateChanged(QTextToSpeech::State state);

private:
    // Speech recognition
    QAudioInput *m_audioInput;
    QMediaRecorder *m_recorder;
    QTemporaryFile *m_tempFile;
    QTimer *m_volumeTimer;
    bool m_isRecording;
    double m_recordingVolume;
    QString m_currentLanguage;

    // Text-to-speech
    QTextToSpeech *m_tts;
    bool m_isSpeaking;
    QString m_currentVoice;
    double m_speechRate;
    double m_pitch;
    double m_volume;

    // Settings
    QVariantMap m_voiceSettings;
    void initializeRecorder();
    void initializeTTS();
    void processSpeechRecognition(const QString &audioFilePath);
};

#endif // VOICESERVICE_H