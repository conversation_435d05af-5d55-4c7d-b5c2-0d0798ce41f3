#include "anthropic_provider.h"
#include <QDebug>
#include <QUrlQuery>
#include <QNetworkRequest>

AnthropicProvider::Anthropic<PERSON>rovider(QObject *parent)
    : Provider(parent)
    , m_networkManager(new QNetworkAccessManager(this))
{
    // Initialize default settings
    m_settings["apiKey"] = "";
    m_settings["model"] = "claude-3-opus-20240229";
    m_settings["temperature"] = 0.7;
    m_settings["maxTokens"] = 2000;
    
    // Connect network manager signals
    connect(m_networkManager, &QNetworkAccessManager::finished, this, &AnthropicProvider::handleNetworkReply);
}

AnthropicProvider::~AnthropicProvider()
{
    // Cancel any pending requests
    for (auto reply : m_activeRequests.keys()) {
        reply->abort();
    }
}

QString AnthropicProvider::name() const
{
    return "anthropic";
}

QString AnthropicProvider::displayName() const
{
    return "Anthropic";
}

bool AnthropicProvider::requiresApiKey() const
{
    return true;
}

void AnthropicProvider::setSettings(const QVariantMap &settings)
{
    // Merge new settings with existing ones
    for (auto it = settings.constBegin(); it != settings.constEnd(); ++it) {
        m_settings[it.key()] = it.value();
    }
}

QVariantMap AnthropicProvider::settings() const
{
    return m_settings;
}

QStringList AnthropicProvider::availableModels() const
{
    return QStringList() 
        << "claude-3-opus-20240229" 
        << "claude-3-sonnet-20240229" 
        << "claude-3-haiku-20240307";
}

void AnthropicProvider::processMessage(const QString &conversationId,
                                      const QVariantList &history,
                                      const QString &message,
                                      const QVariantMap &conversationSettings)
{
    // Check if API key is set
    if (m_settings["apiKey"].toString().isEmpty()) {
        emit errorOccurred(conversationId, "API key is not set. Please set your Anthropic API key in the settings.");
        return;
    }
    
    emit processingStarted(conversationId);
    
    // Prepare the request
    QNetworkRequest request(QUrl("https://api.anthropic.com/v1/messages"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");
    request.setRawHeader("x-api-key", m_settings["apiKey"].toString().toUtf8());
    request.setRawHeader("anthropic-version", "2023-06-01");
    
    // Prepare the request body
    QJsonObject requestObj;
    
    // Use conversation-specific model if provided, otherwise use the default
    if (conversationSettings.contains("model") && !conversationSettings["model"].toString().isEmpty()) {
        requestObj["model"] = conversationSettings["model"].toString();
    } else {
        requestObj["model"] = m_settings["model"].toString();
    }
    
    // Format messages for Anthropic API
    QJsonArray messages = formatMessages(history, message);
    requestObj["messages"] = messages;
    
    // Add system prompt if available in conversation settings or global settings
    if (conversationSettings.contains("systemPrompt") && !conversationSettings["systemPrompt"].toString().isEmpty()) {
        requestObj["system"] = conversationSettings["systemPrompt"].toString();
    } else if (m_settings.contains("systemPrompt") && !m_settings["systemPrompt"].toString().isEmpty()) {
        requestObj["system"] = m_settings["systemPrompt"].toString();
    }
    
    // Use conversation-specific temperature if provided, otherwise use the default
    if (conversationSettings.contains("temperature")) {
        requestObj["temperature"] = conversationSettings["temperature"].toDouble();
    } else {
        requestObj["temperature"] = m_settings["temperature"].toDouble();
    }
    
    // Use conversation-specific max tokens if provided, otherwise use the default
    if (conversationSettings.contains("maxTokens") && conversationSettings["maxTokens"].toInt() > 0) {
        requestObj["max_tokens"] = conversationSettings["maxTokens"].toInt();
    } else if (m_settings.contains("maxTokens") && m_settings["maxTokens"].toInt() > 0) {
        requestObj["max_tokens"] = m_settings["maxTokens"].toInt();
    }
    
    // Add any additional settings from the conversation
    if (conversationSettings.contains("settings")) {
        QVariantMap additionalSettings = conversationSettings["settings"].toMap();
        for (auto it = additionalSettings.constBegin(); it != additionalSettings.constEnd(); ++it) {
            // Skip settings we've already handled
            if (it.key() != "model" && it.key() != "temperature" && it.key() != "maxTokens" && it.key() != "systemPrompt") {
                requestObj[it.key()] = QJsonValue::fromVariant(it.value());
            }
        }
    }
    
    QJsonDocument requestDoc(requestObj);
    QByteArray requestData = requestDoc.toJson();
    
    // Send the request
    QNetworkReply *reply = m_networkManager->post(request, requestData);
    m_activeRequests[reply] = conversationId;
    
    // Debug output
    qDebug() << "Anthropic request sent for conversation:" << conversationId;
}

void AnthropicProvider::handleNetworkReply(QNetworkReply *reply)
{
    // Get the conversation ID for this reply
    QString conversationId = m_activeRequests.value(reply);
    
    // Remove from active requests
    m_activeRequests.remove(reply);
    
    // Handle the reply
    if (reply->error() == QNetworkReply::NoError) {
        QByteArray responseData = reply->readAll();
        QJsonDocument responseDoc = QJsonDocument::fromJson(responseData);
        QJsonObject responseObj = responseDoc.object();
        
        // Extract the response content
        QString content = extractContent(responseObj);
        
        if (!content.isEmpty()) {
            emit responseReceived(conversationId, content);
        } else {
            emit errorOccurred(conversationId, "Failed to extract content from Anthropic response");
        }
    } else {
        // Handle error
        QByteArray responseData = reply->readAll();
        QString errorMessage;
        
        // Try to parse error message from JSON response
        QJsonDocument errorDoc = QJsonDocument::fromJson(responseData);
        if (!errorDoc.isNull() && errorDoc.isObject()) {
            QJsonObject errorObj = errorDoc.object();
            if (errorObj.contains("error")) {
                QJsonObject error = errorObj["error"].toObject();
                if (error.contains("message")) {
                    errorMessage = error["message"].toString();
                }
            }
        }
        
        // If we couldn't parse the error message, use the reply's error string
        if (errorMessage.isEmpty()) {
            errorMessage = reply->errorString();
        }
        
        emit errorOccurred(conversationId, QString("Anthropic API error: %1").arg(errorMessage));
    }
    
    // Signal that processing is finished
    emit processingFinished(conversationId);
    
    // Clean up
    reply->deleteLater();
}

QJsonArray AnthropicProvider::formatMessages(const QVariantList &history, const QString &message)
{
    QJsonArray messages;
    
    // Convert history to Anthropic message format
    for (const QVariant &item : history) {
        QVariantMap messageItem = item.toMap();
        QString role = messageItem["role"].toString();
        
        // Skip system messages as they are handled separately in Anthropic API
        if (role == "system") {
            continue;
        }
        
        // Map roles to Anthropic format
        if (role == "assistant") {
            role = "assistant";
        } else if (role == "user") {
            role = "user";
        } else {
            // Skip unknown roles
            continue;
        }
        
        QJsonObject messageObj;
        messageObj["role"] = role;
        messageObj["content"] = messageItem["content"].toString();
        messages.append(messageObj);
    }
    
    // Add the current user message
    QJsonObject userMessage;
    userMessage["role"] = "user";
    userMessage["content"] = message;
    messages.append(userMessage);
    
    return messages;
}

QString AnthropicProvider::extractContent(const QJsonObject &responseObj)
{
    if (responseObj.contains("content") && responseObj["content"].isArray()) {
        QJsonArray contentArray = responseObj["content"].toArray();
        
        // Anthropic returns an array of content blocks
        QString fullContent;
        for (const QJsonValue &contentValue : contentArray) {
            if (contentValue.isObject()) {
                QJsonObject contentObj = contentValue.toObject();
                if (contentObj["type"].toString() == "text" && contentObj.contains("text")) {
                    fullContent += contentObj["text"].toString();
                }
            }
        }
        
        return fullContent;
    }
    
    return QString();
}