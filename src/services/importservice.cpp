#include "importservice.h"
#include "conversationmanager.h"
#include "database_service.h"
#include <QDir>
#include <QFileInfo>
#include <QJsonArray>
#include <QJsonDocument>
#include <QJsonObject>
#include <QMimeDatabase>
#include <QRegularExpression>
#include <QTextStream>
#include <QUuid>

ImportService::ImportService(QObject *parent)
    : QObject(parent)
{
    m_conversationManager = qobject_cast<ConversationManager*>(parent);
    if (!m_conversationManager) {
        m_conversationManager = ConversationManager::instance();
    }

    m_databaseService = DatabaseService::instance(this);
}

bool ImportService::importConversation(const QString &filePath)
{
    emit importStarted(filePath);

    QString errorMessage;
    if (!validateImportFile(filePath, errorMessage)) {
        emit importFailed(filePath, errorMessage);
        return false;
    }

    ImportFormat format = detectFileFormat(filePath);
    QString conversationId;
    bool success = false;

    try {
        switch (format) {
            case JSON:
                success = importFromJson(filePath, conversationId);
                break;
            case Markdown:
                success = importFromMarkdown(filePath, conversationId);
                break;
            case HTML:
                success = importFromHtml(filePath, conversationId);
                break;
            default:
                emit importFailed(filePath, "Unsupported import format");
                return false;
        }

        if (success) {
            emit importCompleted(filePath, conversationId);
        } else {
            emit importFailed(filePath, "Failed to import conversation");
        }

        return success;
    } catch (const std::exception &e) {
        emit importFailed(filePath, QString("Exception during import: %1").arg(e.what()));
        return false;
    }
}

ImportService::ImportFormat ImportService::detectFileFormat(const QString &filePath) const
{
    QFileInfo fileInfo(filePath);
    QString extension = fileInfo.suffix().toLower();

    if (extension == "json") {
        return JSON;
    } else if (extension == "md") {
        return Markdown;
    } else if (extension == "html" || extension == "htm") {
        return HTML;
    } else {
        // Try to detect by content
        QFile file(filePath);
        if (file.open(QIODevice::ReadOnly | QIODevice::Text)) {
            QTextStream in(&file);
            QString firstLine = in.readLine();
            file.close();

            if (firstLine.startsWith("{") || firstLine.contains("\"id\"") || firstLine.contains("\"title\"")) {
                return JSON;
            } else if (firstLine.startsWith("#") || firstLine.contains("**Created:**")) {
                return Markdown;
            } else if (firstLine.contains("<!DOCTYPE html>") || firstLine.contains("<html>")) {
                return HTML;
            }
        }

        return Unknown;
    }
}

QString ImportService::getFileFilterString() const
{
    return "JSON Files (*.json);;Markdown Files (*.md);;HTML Files (*.html *.htm);;All Files (*)";
}

bool ImportService::validateImportFile(const QString &filePath, QString &errorMessage) const
{
    QFileInfo fileInfo(filePath);

    // Check if file exists
    if (!fileInfo.exists()) {
        errorMessage = "File does not exist";
        return false;
    }

    // Check if file is readable
    if (!fileInfo.isReadable()) {
        errorMessage = "File is not readable";
        return false;
    }

    // Check file size
    if (fileInfo.size() > 50 * 1024 * 1024) { // 50MB limit
        errorMessage = "File is too large (max 50MB)";
        return false;
    }

    ImportFormat format = detectFileFormat(filePath);
    if (format == Unknown) {
        errorMessage = "Unsupported file format";
        return false;
    }

    // For JSON files, validate the structure
    if (format == JSON) {
        QFile file(filePath);
        if (!file.open(QIODevice::ReadOnly)) {
            errorMessage = "Could not open file for reading";
            return false;
        }

        QJsonParseError parseError;
        QJsonDocument doc = QJsonDocument::fromJson(file.readAll(), &parseError);
        file.close();

        if (parseError.error != QJsonParseError::NoError) {
            errorMessage = "Invalid JSON: " + parseError.errorString();
            return false;
        }

        if (!doc.isObject()) {
            errorMessage = "Invalid JSON: Root element must be an object";
            return false;
        }

        QJsonObject obj = doc.object();
        if (!obj.contains("id") || !obj.contains("title") || !obj.contains("messages")) {
            errorMessage = "Invalid JSON: Missing required fields (id, title, messages)";
            return false;
        }

        if (!obj["messages"].isArray()) {
            errorMessage = "Invalid JSON: 'messages' must be an array";
            return false;
        }
    }

    return true;
}

bool ImportService::conversationExists(const QString &conversationId) const
{
    QVariantMap conversation = m_conversationManager->getConversation(conversationId);
    return !conversation.isEmpty();
}

bool ImportService::importFromJson(const QString &filePath, QString &conversationId)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly)) {
        return false;
    }

    QJsonDocument doc = QJsonDocument::fromJson(file.readAll());
    file.close();

    if (!doc.isObject()) {
        return false;
    }

    QJsonObject obj = doc.object();

    // Extract conversation data
    QString id = obj["id"].toString();
    QString title = obj["title"].toString();
    QString provider = obj["provider"].toString();
    QString model = obj["model"].toString();
    QString systemPrompt = obj["system_prompt"].toString();

    // Check if conversation already exists
    if (conversationExists(id)) {
        if (!handleDuplicateConversation(id, conversationId)) {
            return false;
        }
    } else {
        conversationId = id;
    }

    // Create the conversation
    QVariantMap conversationData = createConversation(title, provider, model, systemPrompt);

    // Set the ID to match the imported one
    conversationData["id"] = conversationId;

    // Set creation and update times if available
    if (obj.contains("created_at")) {
        conversationData["createdAt"] = obj["created_at"].toString();
    }

    if (obj.contains("updated_at")) {
        conversationData["updatedAt"] = obj["updated_at"].toString();
    }

    // Save the conversation
    m_databaseService->saveConversation(conversationData);

    // Import messages
    QJsonArray messagesArray = obj["messages"].toArray();
    int messageCount = messagesArray.size();

    for (int i = 0; i < messageCount; i++) {
        QJsonObject messageObj = messagesArray[i].toObject();

        QString messageId = messageObj["id"].toString();
        QString content = messageObj["content"].toString();
        QString role = messageObj["role"].toString();
        QString timestamp = messageObj["timestamp"].toString();

        // Add the message
        QString newMessageId = addMessage(conversationId, content, role, QDateTime::fromString(timestamp, Qt::ISODate));

        // Import attachments if any
        if (messageObj.contains("attachments")) {
            QJsonArray attachmentsArray = messageObj["attachments"].toArray();

            for (const QJsonValue &attachmentVal : attachmentsArray) {
                QJsonObject attachmentObj = attachmentVal.toObject();

                QString attachmentId = attachmentObj["id"].toString();
                QString filename = attachmentObj["filename"].toString();
                QString path = attachmentObj["path"].toString();
                QString type = attachmentObj["type"].toString();
                QString mimeType = attachmentObj["mime_type"].toString();

                // Import the attachment
                QString newPath = importAttachment(path, filename, mimeType);

                if (!newPath.isEmpty()) {
                    // Create attachment data
                    QVariantMap attachmentData;
                    attachmentData["id"] = attachmentId;
                    attachmentData["filename"] = filename;
                    attachmentData["path"] = newPath;
                    attachmentData["type"] = type;
                    attachmentData["mimeType"] = mimeType;

                    if (attachmentObj.contains("size")) {
                        attachmentData["size"] = attachmentObj["size"].toInt();
                    }

                    // Add the attachment to the message
                    m_conversationManager->addAttachment(conversationId, newMessageId, newPath);
                }
            }
        }

        // Report progress
        int progress = ((i + 1) * 100) / messageCount;
        emit importProgress(filePath, progress);
    }

    return true;
}

bool ImportService::importFromMarkdown(const QString &filePath, QString &conversationId)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream in(&file);
    QString content = in.readAll();
    file.close();

    // Parse the markdown content
    QRegularExpression titleRegex("^# (.+)$", QRegularExpression::MultilineOption);
    QRegularExpressionMatch titleMatch = titleRegex.match(content);

    QString title = "Imported Conversation";
    if (titleMatch.hasMatch()) {
        title = titleMatch.captured(1).trimmed();
    }

    // Extract metadata
    QRegularExpression metadataRegex("- \\*\\*([^:]+):\\*\\* (.+)$", QRegularExpression::MultilineOption);
    QRegularExpressionMatchIterator metadataMatches = metadataRegex.globalMatch(content);

    QString provider = "unknown";
    QString model = "unknown";
    QString systemPrompt = "";

    while (metadataMatches.hasNext()) {
        QRegularExpressionMatch match = metadataMatches.next();
        QString key = match.captured(1).trimmed();
        QString value = match.captured(2).trimmed();

        if (key == "Provider") {
            provider = value;
        } else if (key == "Model") {
            model = value;
        }
    }

    // Extract system prompt if present
    QRegularExpression systemPromptRegex("## System Prompt\\s+(.+?)\\s+##", QRegularExpression::DotMatchesEverythingOption);
    QRegularExpressionMatch systemPromptMatch = systemPromptRegex.match(content);

    if (systemPromptMatch.hasMatch()) {
        systemPrompt = systemPromptMatch.captured(1).trimmed();
    }

    // Create the conversation
    conversationId = generateUniqueId();
    QVariantMap conversationData = createConversation(title, provider, model, systemPrompt);

    // Save the conversation
    m_databaseService->saveConversation(conversationData);

    // Extract messages
    QRegularExpression messageRegex("### ([^\\(]+) \\(([^\\)]+)\\)\\s+(.+?)(?=### |$)",
                                   QRegularExpression::DotMatchesEverythingOption);
    QRegularExpressionMatchIterator messageMatches = messageRegex.globalMatch(content);

    int messageCount = 0;
    int processedCount = 0;

    // Count messages first
    while (messageMatches.hasNext()) {
        messageMatches.next();
        messageCount++;
    }

    // Reset and process messages
    messageMatches = messageRegex.globalMatch(content);

    while (messageMatches.hasNext()) {
        QRegularExpressionMatch match = messageMatches.next();
        QString role = match.captured(1).trimmed();
        QString timestamp = match.captured(2).trimmed();
        QString messageContent = match.captured(3).trimmed();

        // Convert role string to role enum
        QString roleStr = "user";
        if (role.compare("Assistant", Qt::CaseInsensitive) == 0) {
            roleStr = "assistant";
        } else if (role.compare("System", Qt::CaseInsensitive) == 0) {
            roleStr = "system";
        }

        // Parse timestamp
        QDateTime dateTime = QDateTime::fromString(timestamp, "yyyy-MM-dd HH:mm:ss");

        // Add the message
        addMessage(conversationId, messageContent, roleStr, dateTime);

        // Report progress
        processedCount++;
        int progress = (processedCount * 100) / messageCount;
        emit importProgress(filePath, progress);
    }

    return true;
}

bool ImportService::importFromHtml(const QString &filePath, QString &conversationId)
{
    // HTML import is more complex and would require an HTML parser
    // For simplicity, we'll implement a basic version that extracts content from specific HTML elements

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        return false;
    }

    QTextStream in(&file);
    QString content = in.readAll();
    file.close();

    // Extract title
    QRegularExpression titleRegex("<title>([^<]+)</title>");
    QRegularExpressionMatch titleMatch = titleRegex.match(content);

    QString title = "Imported Conversation";
    if (titleMatch.hasMatch()) {
        title = titleMatch.captured(1).trimmed();
    }

    // Extract metadata
    QRegularExpression providerRegex("<strong>Provider:</strong>\\s*([^<]+)");
    QRegularExpression modelRegex("<strong>Model:</strong>\\s*([^<]+)");

    QRegularExpressionMatch providerMatch = providerRegex.match(content);
    QRegularExpressionMatch modelMatch = modelRegex.match(content);

    QString provider = "unknown";
    QString model = "unknown";

    if (providerMatch.hasMatch()) {
        provider = providerMatch.captured(1).trimmed();
    }

    if (modelMatch.hasMatch()) {
        model = modelMatch.captured(1).trimmed();
    }

    // Extract system prompt if present
    QString systemPrompt = "";
    QRegularExpression systemPromptRegex("<h2>System Prompt</h2>\\s*<p>([^<]+)</p>");
    QRegularExpressionMatch systemPromptMatch = systemPromptRegex.match(content);

    if (systemPromptMatch.hasMatch()) {
        systemPrompt = systemPromptMatch.captured(1).trimmed();
        systemPrompt = systemPrompt.replace("<br>", "\n");
    }

    // Create the conversation
    conversationId = generateUniqueId();
    QVariantMap conversationData = createConversation(title, provider, model, systemPrompt);

    // Save the conversation
    m_databaseService->saveConversation(conversationData);

    // Extract messages
    QRegularExpression messageRegex("<div class=\"message ([^\"]+)\">\\s*<div class=\"message-header\">\\s*([^<]+)<span class=\"timestamp\">([^<]+)</span>\\s*</div>\\s*<div class=\"content\">([^<]+)(?:</div>|<br>|<div class=\"attachment\">)");
    QRegularExpressionMatchIterator messageMatches = messageRegex.globalMatch(content);

    int messageCount = 0;
    int processedCount = 0;

    // Count messages first
    while (messageMatches.hasNext()) {
        messageMatches.next();
        messageCount++;
    }

    // Reset and process messages
    messageMatches = messageRegex.globalMatch(content);

    while (messageMatches.hasNext()) {
        QRegularExpressionMatch match = messageMatches.next();
        QString roleClass = match.captured(1).trimmed();
        QString role = match.captured(2).trimmed();
        QString timestamp = match.captured(3).trimmed();
        QString messageContent = match.captured(4).trimmed();

        // Convert role string to role enum
        QString roleStr = "user";
        if (role.startsWith("Assistant")) {
            roleStr = "assistant";
        } else if (role.startsWith("System")) {
            roleStr = "system";
        }

        // Parse timestamp
        QDateTime dateTime = QDateTime::fromString(timestamp, "yyyy-MM-dd HH:mm:ss");

        // Clean up content (replace HTML entities)
        messageContent = messageContent.replace("&lt;", "<")
                                      .replace("&gt;", ">")
                                      .replace("&amp;", "&")
                                      .replace("&quot;", "\"")
                                      .replace("&apos;", "'")
                                      .replace("<br>", "\n");

        // Add the message
        addMessage(conversationId, messageContent, roleStr, dateTime);

        // Report progress
        processedCount++;
        int progress = (processedCount * 100) / messageCount;
        emit importProgress(filePath, progress);
    }

    return true;
}

QString ImportService::importAttachment(const QString &sourcePath, const QString &filename, const QString &mimeType)
{
    QFileInfo sourceInfo(sourcePath);

    // If source file doesn't exist, skip it
    if (!sourceInfo.exists() || !sourceInfo.isReadable()) {
        return QString();
    }

    // Get the attachment storage path from the conversation manager
    QString storagePath = m_conversationManager->getAttachmentStoragePath();

    // Copy the file to the attachment storage
    return m_conversationManager->saveAttachmentFile(sourcePath, filename);
}

QVariantMap ImportService::createConversation(const QString &title, const QString &provider, const QString &model, const QString &systemPrompt)
{
    QVariantMap conversation;
    conversation["id"] = generateUniqueId();
    conversation["title"] = title;
    conversation["provider"] = provider;
    conversation["model"] = model;
    conversation["systemPrompt"] = systemPrompt;
    conversation["createdAt"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    conversation["updatedAt"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    return conversation;
}

QString ImportService::addMessage(const QString &conversationId, const QString &content, const QString &role, const QDateTime &timestamp)
{
    // Create a message
    QVariantMap message;
    message["id"] = generateUniqueId();
    message["content"] = content;
    message["role"] = role;

    if (timestamp.isValid()) {
        message["timestamp"] = timestamp.toString(Qt::ISODate);
    } else {
        message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    }

    // Save the message
    m_databaseService->saveMessage(conversationId, message);

    return message["id"].toString();
}

bool ImportService::handleDuplicateConversation(const QString &conversationId, QString &newConversationId)
{
    // Generate a new ID for the conversation
    newConversationId = generateUniqueId();

    // Get the original conversation data
    QVariantMap originalConversation = m_conversationManager->getConversation(conversationId);

    // Update the title to indicate it's a duplicate
    QString title = originalConversation["title"].toString();
    originalConversation["title"] = title + " (Imported)";

    return true;
}

QString ImportService::generateUniqueId() const
{
    return QUuid::createUuid().toString(QUuid::WithoutBraces);
}