#ifndef OPENAI_PROVIDER_H
#define OPENAI_PROVIDER_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QNetworkAccessManager>
#include "chamberservices_global.h"

#include <QNetworkReply>
#include "chamberservices_global.h"

#include <QJsonDocument>
#include "chamberservices_global.h"

#include <QJsonObject>
#include <QJsonArray>

#include "provider.h"

class OpenAIProvider : public Provider
{
    Q_OBJECT

public:
    explicit OpenAIProvider(QObject *parent = nullptr);
    ~OpenAIProvider();

    QString name() const override;
    QString displayName() const override;
    bool requiresApiKey() const override;

    void setSettings(const QVariantMap &settings) override;
    QVariantMap settings() const override;

    QStringList availableModels() const override;
    void processMessage(const QString &conversationId,
                         const QVariantList &history,
                         const QString &message,
                         const QVariantMap &conversationSettings = QVariantMap()) override;

private slots:
    void handleNetworkReply(QNetworkReply *reply);

private:
    QVariantMap m_settings;
    QNetworkAccessManager *m_networkManager;

    // Map to track ongoing requests by conversation ID
    QMap<QNetworkReply*, QString> m_activeRequests;

    // Helper methods
    QJsonArray formatMessages(const QVariantList &history, const QString &message);
    QString extractContent(const QJsonObject &responseObj);
};

#endif // OPENAI_PROVIDER_H