#ifndef EXPORTSERVICE_H
#define EXPORTSERVICE_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QVariantList>
#include "chamberservices_global.h"

#include <QFile>
#include "chamberservices_global.h"

#include <QTextStream>
#include "chamberservices_global.h"

#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QUrl>
#include <QDir>
#include <QFileInfo>
#include <QStandardPaths>

class ConversationManager;

class CHAMBERSERVICES_EXPORT ExportService : public QObject
{
    Q_OBJECT

public:
    enum ExportFormat {
        JSON,
        Markdown,
        HTML,
        PDF
    };
    Q_ENUM(ExportFormat)

    explicit ExportService(QObject *parent = nullptr);
    
    // Export a conversation to a file
    Q_INVOKABLE bool exportConversation(const QString &conversationId, 
                                       const QString &filePath, 
                                       ExportFormat format);
    
    // Get the default export directory
    Q_INVOKABLE QString getDefaultExportDirectory() const;
    
    // Get the suggested filename for a conversation export
    Q_INVOKABLE QString getSuggestedFilename(const QString &conversationId, ExportFormat format) const;
    
    // Get the file filter string for file dialogs
    Q_INVOKABLE QString getFileFilterString() const;
    
    // Convert ExportFormat enum to string
    Q_INVOKABLE QString formatToString(ExportFormat format) const;
    
    // Convert string to ExportFormat enum
    Q_INVOKABLE ExportFormat stringToFormat(const QString &formatStr) const;

signals:
    void exportStarted(const QString &conversationId);
    void exportProgress(const QString &conversationId, int progress);
    void exportCompleted(const QString &conversationId, const QString &filePath);
    void exportFailed(const QString &conversationId, const QString &error);

private:
    ConversationManager *m_conversationManager;
    
    // Export methods for different formats
    bool exportToJson(const QString &conversationId, const QString &filePath);
    bool exportToMarkdown(const QString &conversationId, const QString &filePath);
    bool exportToHtml(const QString &conversationId, const QString &filePath);
    bool exportToPdf(const QString &conversationId, const QString &filePath);
    
    // Helper methods
    QString getConversationTitle(const QString &conversationId) const;
    QVariantMap getConversationData(const QString &conversationId) const;
    QVariantList getConversationMessages(const QString &conversationId) const;
    QString sanitizeFilename(const QString &filename) const;
    QString copyAttachmentForExport(const QString &originalPath, const QString &exportDir, const QString &filename) const;
    QString generateHtmlContent(const QVariantMap &conversation, const QVariantList &messages) const;
    QString messageToHtml(const QVariantMap &message) const;
    QString messageToMarkdown(const QVariantMap &message) const;
    QString attachmentToHtml(const QVariantMap &attachment) const;
    QString attachmentToMarkdown(const QVariantMap &attachment) const;
    QString roleToString(const QString &role) const;
};

#endif // EXPORTSERVICE_H