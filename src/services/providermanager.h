#ifndef PROVIDERMANAGER_H
#define PROVIDERMANAGER_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QMap>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QVariantList>
#include <memory>

#include "database_service.h"
#include "provider.h"

class CHAMBERSERVICES_EXPORT ProviderManager : public QObject
{
    Q_OBJECT

public:
    explicit ProviderManager(QObject *parent = nullptr);
    
    QVariantList availableProviders() const;
    
    bool hasProvider(const QString &name) const;
    void registerProvider(Provider *provider);
    
    Provider* currentProvider() const;
    QString currentProviderName() const;
    void setCurrentProvider(const QString &name);
    
    void setProviderSettings(const QString &name, const QVariantMap &settings);
    QVariantMap providerSettings(const QString &name) const;
    void processMessage(const QString &conversationId,
                       const QVariantList &history,
                       const QString &message,
                       const QVariantMap &conversationSettings = QVariantMap());

signals:
    void providerRegistered(const QString &name);
    void currentProviderChanged(const QString &name);
    void responseReceived(const QString &conversationId, const QString &response);
    void errorOccurred(const QString &conversationId, const QString &error);
    void processingStarted(const QString &conversationId);
    void processingFinished(const QString &conversationId);

private:
    QMap<QString, Provider*> m_providers;
    QString m_currentProviderName;
    DatabaseService *m_databaseService;
    
    // Placeholder provider for when no real provider is available
    class PlaceholderProvider;
    std::unique_ptr<Provider> m_placeholderProvider;
    
    // Helper methods for API key management
    void loadProviderKeys();
    void saveProviderKey(const QString &provider, const QString &key);
};

#endif // PROVIDERMANAGER_H