#include "cacheservice.h"
#include <QDebug>
#include <QCoreApplication>
#include <QStandardPaths>
#include <QDir>
#include <QFile>
#include <QJsonDocument>
#include <QJsonObject>
#include <QTimer>
#include <QDateTime>

CacheService::CacheService(QObject *parent)
    : QObject(parent)
    , m_persistentCacheEnabled(true)
    , m_autoSaveTimer(new QTimer(this))
{
    // Calculate default cache size based on system memory
    // Default to 5% of system memory or at least 50MB, max 500MB
    const qint64 systemMemory = getSystemMemoryMB();
    const int defaultCacheSize = qBound(50, static_cast<int>(systemMemory * 0.05), 500);

    // Set default cache size
    m_cache.setMaxCost(defaultCacheSize * 1024 * 1024);

    qDebug() << "Cache initialized with size:" << defaultCacheSize << "MB";

    // Set up auto-save timer for persistent cache
    m_autoSaveTimer->setInterval(60000); // Save every minute
    connect(m_autoSaveTimer, &QTimer::timeout, this, &CacheService::saveCacheToDisk);
    m_autoSaveTimer->start();

    // Load cache from disk if available
    loadCacheFromDisk();

    // Connect to aboutToQuit signal to save cache before application closes
    connect(QCoreApplication::instance(), &QCoreApplication::aboutToQuit,
            this, &CacheService::saveCacheToDisk);
}

void CacheService::setCacheSize(int size)
{
    // Size in MB
    m_cache.setMaxCost(size * 1024 * 1024);
    qDebug() << "Cache size set to:" << size << "MB";
}

int CacheService::cacheSize() const
{
    // Return size in MB
    return m_cache.maxCost() / (1024 * 1024);
}

bool CacheService::contains(const QString &key) const
{
    return m_cache.contains(key);
}

QVariant CacheService::get(const QString &key) const
{
    QVariant *value = m_cache.object(key);
    if (value) {
        return *value;
    }
    return QVariant();
}

void CacheService::insert(const QString &key, const QVariant &value)
{
    // Estimate the cost based on the type of value
    int cost = estimateCost(value);

    // Create a new QVariant on the heap
    QVariant *cachedValue = new QVariant(value);

    // Insert into cache with the estimated cost
    m_cache.insert(key, cachedValue, cost);

    // If the cache is getting full (>80%), trigger cleanup of old items
    if (m_cache.totalCost() > m_cache.maxCost() * 0.8) {
        cleanupOldItems();
    }
}

bool CacheService::remove(const QString &key)
{
    return m_cache.remove(key);
}

void CacheService::clear()
{
    m_cache.clear();

    // Also clear persistent cache
    QDir cacheDir(getCacheDir());
    if (cacheDir.exists()) {
        cacheDir.removeRecursively();
        cacheDir.mkpath(".");
    }
}

void CacheService::setPersistentCacheEnabled(bool enabled)
{
    m_persistentCacheEnabled = enabled;

    if (enabled) {
        m_autoSaveTimer->start();
    } else {
        m_autoSaveTimer->stop();
    }
}

bool CacheService::isPersistentCacheEnabled() const
{
    return m_persistentCacheEnabled;
}

void CacheService::saveCacheToDisk()
{
    if (!m_persistentCacheEnabled) {
        return;
    }

    QDir cacheDir(getCacheDir());
    if (!cacheDir.exists()) {
        cacheDir.mkpath(".");
    }

    // Save cache metadata
    QJsonObject metadata;
    metadata["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    metadata["version"] = "1.0";

    QFile metadataFile(cacheDir.filePath("metadata.json"));
    if (metadataFile.open(QIODevice::WriteOnly)) {
        metadataFile.write(QJsonDocument(metadata).toJson());
        metadataFile.close();
    }

    qDebug() << "Cache saved to disk at:" << QDateTime::currentDateTime().toString();
}

void CacheService::loadCacheFromDisk()
{
    if (!m_persistentCacheEnabled) {
        return;
    }

    QDir cacheDir(getCacheDir());
    if (!cacheDir.exists()) {
        return;
    }

    QFile metadataFile(cacheDir.filePath("metadata.json"));
    if (metadataFile.exists() && metadataFile.open(QIODevice::ReadOnly)) {
        QJsonDocument doc = QJsonDocument::fromJson(metadataFile.readAll());
        metadataFile.close();

        if (doc.isObject()) {
            QJsonObject metadata = doc.object();
            QString timestamp = metadata["timestamp"].toString();
            QString version = metadata["version"].toString();

            qDebug() << "Loading cache from disk, saved at:" << timestamp << "version:" << version;
        }
    }
}

QString CacheService::getCacheDir() const
{
    return QStandardPaths::writableLocation(QStandardPaths::CacheLocation) + "/chamberui";
}

qint64 CacheService::getSystemMemoryMB() const
{
    // Default to 8GB if we can't determine system memory
    // In a real implementation, you would use platform-specific APIs to get actual memory
    return 8 * 1024;
}

int CacheService::estimateCost(const QVariant &value) const
{
    int cost = 0;

    switch (value.typeId()) {
    case QMetaType::QString:
        cost = value.toString().size() * sizeof(QChar);
        break;
    case QMetaType::QByteArray:
        cost = value.toByteArray().size();
        break;
    case QMetaType::QVariantMap: {
        // Better estimate for maps
        const QVariantMap map = value.toMap();
        cost = 0;
        for (auto it = map.constBegin(); it != map.constEnd(); ++it) {
            cost += it.key().size() * sizeof(QChar);
            cost += estimateCost(it.value());
        }
        break;
    }
    case QMetaType::QVariantList: {
        // Better estimate for lists
        const QVariantList list = value.toList();
        cost = 0;
        for (const QVariant &item : list) {
            cost += estimateCost(item);
        }
        break;
    }
    default:
        // Default cost for other types
        cost = 100;
        break;
    }

    return cost;
}

void CacheService::cleanupOldItems()
{
    // This would implement a more sophisticated cleanup strategy
    // For now, we rely on QCache's built-in LRU mechanism
    qDebug() << "Cache cleanup triggered. Current usage:"
             << (m_cache.totalCost() / (1024.0 * 1024.0)) << "MB of"
             << (m_cache.maxCost() / (1024.0 * 1024.0)) << "MB";
}