#ifndef IMPORTSERVICE_H
#define IMPORTSERVICE_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QVariantList>
#include "chamberservices_global.h"

#include <QFile>
#include "chamberservices_global.h"

#include <QJsonDocument>
#include "chamberservices_global.h"

#include <QJsonObject>
#include <QJsonArray>
#include <QDateTime>
#include <QUrl>
#include <QDir>
#include <QFileInfo>

class ConversationManager;
class DatabaseService;

class CHAMBERSERVICES_EXPORT ImportService : public QObject
{
    Q_OBJECT

public:
    enum ImportFormat {
        JSON,
        Markdown,
        HTML,
        Unknown
    };
    Q_ENUM(ImportFormat)

    explicit ImportService(QObject *parent = nullptr);
    
    // Import a conversation from a file
    Q_INVOKABLE bool importConversation(const QString &filePath);
    
    // Detect the format of a file
    Q_INVOKABLE ImportFormat detectFileFormat(const QString &filePath) const;
    
    // Get the file filter string for file dialogs
    Q_INVOKABLE QString getFileFilterString() const;
    
    // Validate an import file
    Q_INVOKABLE bool validateImportFile(const QString &filePath, QString &errorMessage) const;
    
    // Check if a conversation already exists
    Q_INVOKABLE bool conversationExists(const QString &conversationId) const;

signals:
    void importStarted(const QString &filePath);
    void importProgress(const QString &filePath, int progress);
    void importCompleted(const QString &filePath, const QString &conversationId);
    void importFailed(const QString &filePath, const QString &error);

private:
    ConversationManager *m_conversationManager;
    DatabaseService *m_databaseService;
    
    // Import methods for different formats
    bool importFromJson(const QString &filePath, QString &conversationId);
    bool importFromMarkdown(const QString &filePath, QString &conversationId);
    bool importFromHtml(const QString &filePath, QString &conversationId);
    
    // Helper methods
    QString importAttachment(const QString &sourcePath, const QString &filename, const QString &mimeType);
    QVariantMap createConversation(const QString &title, const QString &provider, const QString &model, const QString &systemPrompt);
    QString addMessage(const QString &conversationId, const QString &content, const QString &role, const QDateTime &timestamp);
    bool handleDuplicateConversation(const QString &conversationId, QString &newConversationId);
    QString generateUniqueId() const;
};

#endif // IMPORTSERVICE_H