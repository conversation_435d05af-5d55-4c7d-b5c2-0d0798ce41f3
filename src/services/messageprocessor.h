#ifndef MESSAGEPROCESSOR_H
#define MESSAGEPROCESSOR_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariantMap>
#include "chamberservices_global.h"

#include <QVariantList>

class ProviderManager;

class CHAMBERSERVICES_EXPORT MessageProcessor : public QObject
{
    Q_OBJECT

public:
    explicit MessageProcessor(QObject *parent = nullptr);
    ~MessageProcessor();
    void processMessage(const QString &conversationId, const QString &message, const QVariantMap &conversationSettings = QVariantMap());
    void setProvider(const QString &provider);
    void setProviderSettings(const QVariantMap &settings);
    
    // Get conversation history for a conversation
    QVariantList getConversationHistory(const QString &conversationId) const;

signals:
    void responseReceived(const QString &conversationId, const QString &response);
    void errorOccurred(const QString &conversationId, const QString &error);
    void processingStarted(const QString &conversationId);
    void processingFinished(const QString &conversationId);

private:
    ProviderManager *m_providerManager;
    
    // Map to store conversation histories
    QMap<QString, QVariantList> m_conversationHistories;
    
    // Helper method to add a message to the conversation history
    void addToHistory(const QString &conversationId, const QString &content, const QString &role);
};

#endif // MESSAGEPROCESSOR_H