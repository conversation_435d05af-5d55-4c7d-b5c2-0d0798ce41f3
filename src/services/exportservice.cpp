#include "exportservice.h"
#include "conversationmanager.h"
#include <QFileInfo>
#include <QDir>
#include <QStandardPaths>
#include <QJsonDocument>
#include <QJsonObject>
#include <QJsonArray>
#include <QTextStream>
#include <QRegularExpression>
#include <QUuid>
#include <QPdfWriter>
#include <QPainter>
#include <QTextDocument>

ExportService::ExportService(QObject *parent)
    : QObject(parent)
{
    m_conversationManager = qobject_cast<ConversationManager*>(parent);
    if (!m_conversationManager) {
        m_conversationManager = ConversationManager::instance();
    }
}

bool ExportService::exportConversation(const QString &conversationId, 
                                     const QString &filePath, 
                                     ExportFormat format)
{
    emit exportStarted(conversationId);
    
    bool success = false;
    
    try {
        switch (format) {
            case JSON:
                success = exportToJson(conversationId, filePath);
                break;
            case Markdown:
                success = exportToMarkdown(conversationId, filePath);
                break;
            case HTML:
                success = exportToHtml(conversationId, filePath);
                break;
            case PDF:
                success = exportToPdf(conversationId, filePath);
                break;
            default:
                emit exportFailed(conversationId, "Unsupported export format");
                return false;
        }
        
        if (success) {
            emit exportCompleted(conversationId, filePath);
        } else {
            emit exportFailed(conversationId, "Failed to export conversation");
        }
        
        return success;
    } catch (const std::exception &e) {
        emit exportFailed(conversationId, QString("Exception during export: %1").arg(e.what()));
        return false;
    }
}

QString ExportService::getDefaultExportDirectory() const
{
    QString documentsPath = QStandardPaths::writableLocation(QStandardPaths::DocumentsLocation);
    QDir dir(documentsPath);
    
    // Create a ChamberUI/Exports directory if it doesn't exist
    if (!dir.exists("ChamberUI/Exports")) {
        dir.mkpath("ChamberUI/Exports");
    }
    
    return dir.filePath("ChamberUI/Exports");
}

QString ExportService::getSuggestedFilename(const QString &conversationId, ExportFormat format) const
{
    QString title = getConversationTitle(conversationId);
    QString sanitizedTitle = sanitizeFilename(title);
    
    // Add date to filename
    QDateTime now = QDateTime::currentDateTime();
    QString dateStr = now.toString("yyyy-MM-dd");
    
    QString extension;
    switch (format) {
        case JSON:
            extension = "json";
            break;
        case Markdown:
            extension = "md";
            break;
        case HTML:
            extension = "html";
            break;
        case PDF:
            extension = "pdf";
            break;
        default:
            extension = "txt";
    }
    
    return QString("%1_%2.%3").arg(sanitizedTitle).arg(dateStr).arg(extension);
}

QString ExportService::getFileFilterString() const
{
    return "JSON Files (*.json);;Markdown Files (*.md);;HTML Files (*.html);;PDF Files (*.pdf);;All Files (*)";
}

QString ExportService::formatToString(ExportFormat format) const
{
    switch (format) {
        case JSON:
            return "JSON";
        case Markdown:
            return "Markdown";
        case HTML:
            return "HTML";
        case PDF:
            return "PDF";
        default:
            return "Unknown";
    }
}

ExportService::ExportFormat ExportService::stringToFormat(const QString &formatStr) const
{
    if (formatStr.compare("JSON", Qt::CaseInsensitive) == 0) {
        return JSON;
    } else if (formatStr.compare("Markdown", Qt::CaseInsensitive) == 0) {
        return Markdown;
    } else if (formatStr.compare("HTML", Qt::CaseInsensitive) == 0) {
        return HTML;
    } else if (formatStr.compare("PDF", Qt::CaseInsensitive) == 0) {
        return PDF;
    } else {
        return JSON; // Default to JSON
    }
}

bool ExportService::exportToJson(const QString &conversationId, const QString &filePath)
{
    QVariantMap conversation = getConversationData(conversationId);
    QVariantList messages = getConversationMessages(conversationId);
    
    // Create the JSON structure
    QJsonObject jsonObj;
    
    // Add conversation metadata
    jsonObj["id"] = conversation["id"].toString();
    jsonObj["title"] = conversation["title"].toString();
    jsonObj["created_at"] = conversation["createdAt"].toString();
    jsonObj["updated_at"] = conversation["updatedAt"].toString();
    jsonObj["provider"] = conversation["provider"].toString();
    jsonObj["model"] = conversation["model"].toString();
    jsonObj["system_prompt"] = conversation["systemPrompt"].toString();
    
    // Add messages
    QJsonArray messagesArray;
    int messageCount = messages.size();
    
    for (int i = 0; i < messageCount; i++) {
        QVariantMap message = messages[i].toMap();
        QJsonObject messageObj;
        
        messageObj["id"] = message["id"].toString();
        messageObj["content"] = message["content"].toString();
        messageObj["role"] = message["role"].toString();
        messageObj["timestamp"] = message["timestamp"].toString();
        
        // Add attachments
        if (message.contains("attachments")) {
            QVariantList attachments = message["attachments"].toList();
            QJsonArray attachmentsArray;
            
            for (const QVariant &attachmentVar : attachments) {
                QVariantMap attachment = attachmentVar.toMap();
                QJsonObject attachmentObj;
                
                attachmentObj["id"] = attachment["id"].toString();
                attachmentObj["filename"] = attachment["filename"].toString();
                attachmentObj["path"] = attachment["path"].toString();
                attachmentObj["type"] = attachment["type"].toString();
                attachmentObj["mime_type"] = attachment["mimeType"].toString();
                attachmentObj["size"] = attachment["size"].toInt();
                
                attachmentsArray.append(attachmentObj);
            }
            
            messageObj["attachments"] = attachmentsArray;
        }
        
        messagesArray.append(messageObj);
        
        // Report progress
        int progress = ((i + 1) * 100) / messageCount;
        emit exportProgress(conversationId, progress);
    }
    
    jsonObj["messages"] = messagesArray;
    
    // Write to file
    QJsonDocument doc(jsonObj);
    QFile file(filePath);
    
    if (!file.open(QIODevice::WriteOnly)) {
        return false;
    }
    
    file.write(doc.toJson(QJsonDocument::Indented));
    file.close();
    
    return true;
}

bool ExportService::exportToMarkdown(const QString &conversationId, const QString &filePath)
{
    QVariantMap conversation = getConversationData(conversationId);
    QVariantList messages = getConversationMessages(conversationId);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    
    // Write header
    out << "# " << conversation["title"].toString() << "\n\n";
    out << "- **Created:** " << QDateTime::fromString(conversation["createdAt"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") << "\n";
    out << "- **Updated:** " << QDateTime::fromString(conversation["updatedAt"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") << "\n";
    out << "- **Provider:** " << conversation["provider"].toString() << "\n";
    out << "- **Model:** " << conversation["model"].toString() << "\n\n";
    
    if (!conversation["systemPrompt"].toString().isEmpty()) {
        out << "## System Prompt\n\n";
        out << conversation["systemPrompt"].toString() << "\n\n";
    }
    
    out << "## Conversation\n\n";
    
    // Write messages
    int messageCount = messages.size();
    for (int i = 0; i < messageCount; i++) {
        QVariantMap message = messages[i].toMap();
        
        out << "### " << roleToString(message["role"].toString()) << " (" 
            << QDateTime::fromString(message["timestamp"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") << ")\n\n";
        
        out << messageToMarkdown(message) << "\n\n";
        
        // Report progress
        int progress = ((i + 1) * 100) / messageCount;
        emit exportProgress(conversationId, progress);
    }
    
    file.close();
    return true;
}

bool ExportService::exportToHtml(const QString &conversationId, const QString &filePath)
{
    QVariantMap conversation = getConversationData(conversationId);
    QVariantList messages = getConversationMessages(conversationId);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return false;
    }
    
    QTextStream out(&file);
    
    // Create HTML content
    QString htmlContent = generateHtmlContent(conversation, messages);
    out << htmlContent;
    
    file.close();
    return true;
}

bool ExportService::exportToPdf(const QString &conversationId, const QString &filePath)
{
    QVariantMap conversation = getConversationData(conversationId);
    QVariantList messages = getConversationMessages(conversationId);
    
    // Create HTML content for PDF
    QString htmlContent = generateHtmlContent(conversation, messages);
    
    // Create PDF writer
    QPdfWriter pdfWriter(filePath);
    pdfWriter.setPageSize(QPageSize(QPageSize::A4));
    pdfWriter.setPageMargins(QMarginsF(30, 30, 30, 30));
    
    // Create painter and document
    QPainter painter(&pdfWriter);
    QTextDocument document;
    document.setHtml(htmlContent);
    document.setPageSize(pdfWriter.pageLayout().paintRect().size());
    
    // Render document to PDF
    document.drawContents(&painter);
    painter.end();
    
    return true;
}

QString ExportService::getConversationTitle(const QString &conversationId) const
{
    QVariantMap conversation = m_conversationManager->getConversation(conversationId);
    return conversation["title"].toString();
}

QVariantMap ExportService::getConversationData(const QString &conversationId) const
{
    return m_conversationManager->getConversation(conversationId);
}

QVariantList ExportService::getConversationMessages(const QString &conversationId) const
{
    return m_conversationManager->getConversationMessages(conversationId);
}

QString ExportService::sanitizeFilename(const QString &filename) const
{
    // Replace invalid filename characters with underscores
    QString sanitized = filename;
    QRegularExpression invalidChars("[\\\\/:*?\"<>|]");
    sanitized.replace(invalidChars, "_");
    
    // Limit length
    if (sanitized.length() > 50) {
        sanitized = sanitized.left(47) + "...";
    }
    
    return sanitized;
}

QString ExportService::copyAttachmentForExport(const QString &originalPath, const QString &exportDir, const QString &filename) const
{
    QFileInfo fileInfo(originalPath);
    if (!fileInfo.exists()) {
        return QString();
    }
    
    // Create attachments directory if it doesn't exist
    QDir dir(exportDir);
    if (!dir.exists("attachments")) {
        dir.mkdir("attachments");
    }
    
    // Generate unique filename to avoid conflicts
    QString uniqueFilename = QUuid::createUuid().toString(QUuid::WithoutBraces).left(8) + "_" + filename;
    QString destPath = dir.filePath("attachments/" + uniqueFilename);
    
    // Copy the file
    if (QFile::copy(originalPath, destPath)) {
        return destPath;
    }
    
    return QString();
}

QString ExportService::generateHtmlContent(const QVariantMap &conversation, const QVariantList &messages) const
{
    QString html = "<!DOCTYPE html>\n"
                   "<html>\n"
                   "<head>\n"
                   "    <meta charset=\"UTF-8\">\n"
                   "    <title>" + conversation["title"].toString() + "</title>\n"
                   "    <style>\n"
                   "        body { font-family: Arial, sans-serif; margin: 20px; }\n"
                   "        .header { margin-bottom: 20px; }\n"
                   "        .message { margin-bottom: 20px; padding: 10px; border-radius: 5px; }\n"
                   "        .user { background-color: #f0f0f0; }\n"
                   "        .assistant { background-color: #e6f7ff; }\n"
                   "        .system { background-color: #fff3e0; }\n"
                   "        .message-header { font-weight: bold; margin-bottom: 5px; }\n"
                   "        .timestamp { color: #666; font-size: 0.8em; }\n"
                   "        .attachment { margin-top: 10px; padding: 5px; background-color: #f9f9f9; border-radius: 3px; }\n"
                   "        pre { background-color: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }\n"
                   "        code { font-family: monospace; }\n"
                   "    </style>\n"
                   "</head>\n"
                   "<body>\n"
                   "    <div class=\"header\">\n"
                   "        <h1>" + conversation["title"].toString() + "</h1>\n"
                   "        <p><strong>Created:</strong> " + QDateTime::fromString(conversation["createdAt"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") + "</p>\n"
                   "        <p><strong>Updated:</strong> " + QDateTime::fromString(conversation["updatedAt"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") + "</p>\n"
                   "        <p><strong>Provider:</strong> " + conversation["provider"].toString() + "</p>\n"
                   "        <p><strong>Model:</strong> " + conversation["model"].toString() + "</p>\n";
    
    if (!conversation["systemPrompt"].toString().isEmpty()) {
        html += "        <div class=\"system-prompt\">\n"
                "            <h2>System Prompt</h2>\n"
                "            <p>" + conversation["systemPrompt"].toString().toHtmlEscaped().replace("\n", "<br>") + "</p>\n"
                "        </div>\n";
    }
    
    html += "    </div>\n"
            "    <h2>Conversation</h2>\n";
    
    // Add messages
    int messageCount = messages.size();
    for (int i = 0; i < messageCount; i++) {
        QVariantMap message = messages[i].toMap();
        QString role = message["role"].toString();
        QString cssClass = role.toLower();
        
        html += "    <div class=\"message " + cssClass + "\">\n"
                "        <div class=\"message-header\">\n"
                "            " + roleToString(role) + " <span class=\"timestamp\">" + 
                QDateTime::fromString(message["timestamp"].toString(), Qt::ISODate).toString("yyyy-MM-dd HH:mm:ss") + "</span>\n"
                "        </div>\n"
                "        " + messageToHtml(message) + "\n"
                "    </div>\n";
        
        // Report progress
        int progress = ((i + 1) * 100) / messageCount;
        emit const_cast<ExportService*>(this)->exportProgress(conversation["id"].toString(), progress);
    }
    
    html += "</body>\n"
            "</html>";
    
    return html;
}

QString ExportService::messageToHtml(const QVariantMap &message) const
{
    QString content = message["content"].toString();
    
    // Convert markdown-style code blocks to HTML
    QRegularExpression codeBlockRegex("```([\\w]*)\\n([\\s\\S]*?)```");
    QRegularExpressionMatchIterator matches = codeBlockRegex.globalMatch(content);
    
    while (matches.hasNext()) {
        QRegularExpressionMatch match = matches.next();
        QString language = match.captured(1);
        QString code = match.captured(2);
        
        QString replacement = "<pre><code class=\"language-" + language + "\">" + 
                             code.toHtmlEscaped() + "</code></pre>";
        
        content.replace(match.captured(0), replacement);
    }
    
    // Convert inline code
    QRegularExpression inlineCodeRegex("`([^`]+)`");
    content.replace(inlineCodeRegex, "<code>\\1</code>");
    
    // Convert newlines to <br>
    content.replace("\n", "<br>");
    
    QString html = "<div class=\"content\">" + content + "</div>";
    
    // Add attachments if any
    if (message.contains("attachments")) {
        QVariantList attachments = message["attachments"].toList();
        
        for (const QVariant &attachmentVar : attachments) {
            QVariantMap attachment = attachmentVar.toMap();
            html += attachmentToHtml(attachment);
        }
    }
    
    return html;
}

QString ExportService::messageToMarkdown(const QVariantMap &message) const
{
    QString markdown = message["content"].toString();
    
    // Add attachments if any
    if (message.contains("attachments")) {
        QVariantList attachments = message["attachments"].toList();
        
        if (!attachments.isEmpty()) {
            markdown += "\n\n**Attachments:**\n\n";
            
            for (const QVariant &attachmentVar : attachments) {
                QVariantMap attachment = attachmentVar.toMap();
                markdown += attachmentToMarkdown(attachment) + "\n";
            }
        }
    }
    
    return markdown;
}

QString ExportService::attachmentToHtml(const QVariantMap &attachment) const
{
    QString type = attachment["type"].toString();
    QString filename = attachment["filename"].toString();
    QString path = attachment["path"].toString();
    
    QString html = "<div class=\"attachment\">\n"
                   "    <strong>Attachment:</strong> " + filename + " (" + type + ")\n";
    
    if (type == "Image") {
        // For images, embed them directly if possible
        html += "    <div><img src=\"" + path + "\" alt=\"" + filename + "\" style=\"max-width: 100%; max-height: 300px;\"></div>\n";
    }
    
    html += "</div>\n";
    
    return html;
}

QString ExportService::attachmentToMarkdown(const QVariantMap &attachment) const
{
    QString type = attachment["type"].toString();
    QString filename = attachment["filename"].toString();
    
    QString markdown = "- " + filename + " (" + type + ")";
    
    if (type == "Image") {
        markdown += " ![" + filename + "](" + attachment["path"].toString() + ")";
    }
    
    return markdown;
}

QString ExportService::roleToString(const QString &role) const
{
    if (role == "user") {
        return "User";
    } else if (role == "assistant") {
        return "Assistant";
    } else if (role == "system") {
        return "System";
    } else {
        return role;
    }
}