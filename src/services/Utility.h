#pragma once

#include <array>
#include <span>
#include <string>
#include <string_view>

// 编译期拼接多个字符串，用','分隔
template <std::size_t... Ns>
consteval auto concat_fields(const char (&...strs)[Ns]) {
  constexpr std::size_t num_strs = sizeof...(Ns);
  constexpr std::size_t total_len =
      (0 + ... + (Ns - 1)) + (num_strs - 1) + 1; // 各字段长-1 + ','数量 + '\0'
  std::array<char, total_len> result{};
  std::size_t pos = 0;

  std::size_t index = 0;
  auto append = [&](const char *s, std::size_t len) {
    for (std::size_t i = 0; i < len; ++i) {
      result[pos++] = s[i];
    }
    if (index++ < num_strs - 1) {
      result[pos++] = ',';
    }
  };

  (append(strs, Ns - 1), ...);
  result[pos] = '\0';
  return result;
}

inline std::string_view
join_fields_once(std::span<const std::string_view> fields) {
  static std::string result;
  if (!result.empty())
    return result;

  for (size_t i = 0; i < fields.size(); ++i) {
    if (i > 0)
      result += ',';
    result += fields[i];
  }
  return result;
}