#include "messageprocessor.h"
#include "providermanager.h"
#include <QTimer>
#include <QRandomGenerator>
#include <QDateTime>

MessageProcessor::MessageProcessor(QObject *parent)
    : QObject(parent)
    , m_providerManager(new ProviderManager(this))
{
    // Connect provider manager signals to our signals
    connect(m_providerManager, &ProviderManager::responseReceived,
            this, &MessageProcessor::responseReceived);
    connect(m_providerManager, &ProviderManager::errorOccurred,
            this, &MessageProcessor::errorOccurred);
    connect(m_providerManager, &ProviderManager::processingStarted,
            this, &MessageProcessor::processingStarted);
    connect(m_providerManager, &ProviderManager::processingFinished,
            this, &MessageProcessor::processingFinished);
}

MessageProcessor::~MessageProcessor()
{
    // ProviderManager will be deleted automatically since it's a child QObject
}
void MessageProcessor::processMessage(const QString &conversationId, const QString &message, const QVariantMap &conversationSettings)
{
    // Add user message to history
    addToHistory(conversationId, message, "user");
    
    // Get conversation history
    QVariantList history = getConversationHistory(conversationId);
    
    // Process message using provider manager
    m_providerManager->processMessage(conversationId, history, message, conversationSettings);
    
    // Note: The response will be received via the connected signals
}

void MessageProcessor::setProvider(const QString &provider)
{
    m_providerManager->setCurrentProvider(provider);
}

void MessageProcessor::setProviderSettings(const QVariantMap &settings)
{
    m_providerManager->setProviderSettings(m_providerManager->currentProviderName(), settings);
}

QVariantList MessageProcessor::getConversationHistory(const QString &conversationId) const
{
    return m_conversationHistories.value(conversationId, QVariantList());
}

void MessageProcessor::addToHistory(const QString &conversationId, const QString &content, const QString &role)
{
    // Create message object
    QVariantMap message;
    message["content"] = content;
    message["role"] = role;
    message["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);
    
    // Get current history
    QVariantList history = m_conversationHistories.value(conversationId, QVariantList());
    
    // Add message to history
    history.append(message);
    
    // Update history
    m_conversationHistories[conversationId] = history;
    
    // If this is an assistant response, emit the signal
    if (role == "assistant") {
        emit responseReceived(conversationId, content);
    }
}

// Connect to the ProviderManager's signals to handle responses
// When a response is received from the provider, add it to the history
// This is done via the signal connections in the constructor