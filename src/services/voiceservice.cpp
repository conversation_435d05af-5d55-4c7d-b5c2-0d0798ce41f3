#include "voiceservice.h"
#include <QDebug>
#include <QDir>
#include <QStandardPaths>
#include <QUrl>
#include <QFile>
#include <QSettings>
#include <QMediaCaptureSession>
#include <QAudioDevice>
#include <QMediaDevices>
#include <QMediaFormat>

VoiceService::VoiceService(QObject *parent)
    : QObject(parent),
      m_audioInput(nullptr),
      m_recorder(nullptr),
      m_tempFile(nullptr),
      m_volumeTimer(nullptr),
      m_isRecording(false),
      m_recordingVolume(0.0),
      m_currentLanguage("en-US"),
      m_tts(nullptr),
      m_isSpeaking(false),
      m_currentVoice(""),
      m_speechRate(1.0),
      m_pitch(1.0),
      m_volume(1.0)
{
    // Initialize text-to-speech
    initializeTTS();

    // Load settings
    loadSettings();
}

VoiceService::~VoiceService()
{
    if (m_isRecording) {
        stopRecording();
    }

    if (m_isSpeaking) {
        stopSpeech();
    }

    if (m_tempFile) {
        m_tempFile->remove();
        delete m_tempFile;
    }

    delete m_audioInput;
    delete m_recorder;
    delete m_volumeTimer;
    delete m_tts;
}

bool VoiceService::isRecording() const
{
    return m_isRecording;
}

double VoiceService::recordingVolume() const
{
    return m_recordingVolume;
}

QStringList VoiceService::availableLanguages() const
{
    // Return a list of supported languages for speech recognition
    return QStringList() << "en-US" << "en-GB" << "fr-FR" << "de-DE" << "es-ES" << "it-IT" << "ja-JP" << "zh-CN";
}

bool VoiceService::isSpeaking() const
{
    return m_isSpeaking;
}

QStringList VoiceService::availableVoices() const
{
    QStringList voices;
    if (m_tts) {
        for (const QVoice &voice : m_tts->availableVoices()) {
            voices << voice.name();
        }
    }
    return voices;
}

QVariantMap VoiceService::voiceSettings() const
{
    return m_voiceSettings;
}

void VoiceService::setVoiceSettings(const QVariantMap &settings)
{
    m_voiceSettings = settings;

    // Apply settings
    if (settings.contains("voice")) {
        setVoice(settings["voice"].toString());
    }

    if (settings.contains("speechRate")) {
        setSpeechRate(settings["speechRate"].toDouble());
    }

    if (settings.contains("pitch")) {
        setPitch(settings["pitch"].toDouble());
    }

    if (settings.contains("volume")) {
        setVolume(settings["volume"].toDouble());
    }

    if (settings.contains("language")) {
        setLanguage(settings["language"].toString());
    }
}

void VoiceService::startRecording()
{
    if (m_isRecording) {
        return;
    }

    // Initialize recorder if needed
    if (!m_recorder) {
        initializeRecorder();
    }

    // Create a temporary file for recording
    if (m_tempFile) {
        m_tempFile->remove();
        delete m_tempFile;
    }

    m_tempFile = new QTemporaryFile(QDir::tempPath() + "/voice_recording_XXXXXX.wav");
    if (!m_tempFile->open()) {
        emit recordingError("Failed to create temporary file for recording");
        return;
    }

    // Set up the recorder
    m_recorder->setOutputLocation(QUrl::fromLocalFile(m_tempFile->fileName()));

    // Start recording
    m_recorder->record();
    m_isRecording = true;
    emit recordingStateChanged();

    // Start volume timer
    if (!m_volumeTimer) {
        m_volumeTimer = new QTimer(this);
        connect(m_volumeTimer, &QTimer::timeout, this, &VoiceService::updateRecordingVolume);
    }
    m_volumeTimer->start(100); // Update every 100ms
}

void VoiceService::stopRecording()
{
    if (!m_isRecording) {
        return;
    }

    // Stop volume timer
    if (m_volumeTimer) {
        m_volumeTimer->stop();
    }

    // Stop recording
    m_recorder->stop();
    m_isRecording = false;
    emit recordingStateChanged();

    // Process the recording for speech recognition
    processSpeechRecognition(m_tempFile->fileName());
}

void VoiceService::cancelRecording()
{
    if (!m_isRecording) {
        return;
    }

    // Stop volume timer
    if (m_volumeTimer) {
        m_volumeTimer->stop();
    }

    // Stop recording
    m_recorder->stop();
    m_isRecording = false;
    emit recordingStateChanged();

    // Don't process the recording
    if (m_tempFile) {
        m_tempFile->remove();
    }
}

void VoiceService::speak(const QString &text)
{
    if (!m_tts) {
        emit speechError("Text-to-speech engine not initialized");
        return;
    }

    // Stop any current speech
    if (m_isSpeaking) {
        stopSpeech();
    }

    // Start speaking
    m_tts->say(text);
    m_isSpeaking = true;
    emit speakingStateChanged();
    emit speechStarted();
}

void VoiceService::pauseSpeech()
{
    if (!m_tts || !m_isSpeaking) {
        return;
    }

    m_tts->pause();
    emit speechPaused();
}

void VoiceService::resumeSpeech()
{
    if (!m_tts) {
        return;
    }

    m_tts->resume();
    emit speechResumed();
}

void VoiceService::stopSpeech()
{
    if (!m_tts || !m_isSpeaking) {
        return;
    }

    m_tts->stop();
    m_isSpeaking = false;
    emit speakingStateChanged();
    emit speechStopped();
}

void VoiceService::setVoice(const QString &voice)
{
    if (!m_tts) {
        return;
    }

    // Find the voice in available voices
    for (const QVoice &v : m_tts->availableVoices()) {
        if (v.name() == voice) {
            m_tts->setVoice(v);
            m_currentVoice = voice;

            // Update settings
            m_voiceSettings["voice"] = voice;
            break;
        }
    }
}

void VoiceService::setSpeechRate(double rate)
{
    if (!m_tts) {
        return;
    }

    m_tts->setRate(rate);
    m_speechRate = rate;

    // Update settings
    m_voiceSettings["speechRate"] = rate;

    emit speechRateChanged();
}

void VoiceService::setPitch(double pitch)
{
    if (!m_tts) {
        return;
    }

    m_tts->setPitch(pitch);
    m_pitch = pitch;

    // Update settings
    m_voiceSettings["pitch"] = pitch;

    emit pitchChanged();
}

void VoiceService::setVolume(double volume)
{
    if (!m_tts) {
        return;
    }

    m_tts->setVolume(volume);
    m_volume = volume;

    // Update settings
    m_voiceSettings["volume"] = volume;

    emit volumeChanged();
}

void VoiceService::setLanguage(const QString &language)
{
    m_currentLanguage = language;

    // Update settings
    m_voiceSettings["language"] = language;
}

void VoiceService::saveSettings()
{
    QSettings settings;
    settings.beginGroup("Voice");
    settings.setValue("voice", m_currentVoice);
    settings.setValue("speechRate", m_speechRate);
    settings.setValue("pitch", m_pitch);
    settings.setValue("volume", m_volume);
    settings.setValue("language", m_currentLanguage);
    settings.endGroup();
}

void VoiceService::loadSettings()
{
    QSettings settings;
    settings.beginGroup("Voice");

    // Load voice settings
    QVariantMap voiceSettings;
    voiceSettings["voice"] = settings.value("voice", "").toString();
    voiceSettings["speechRate"] = settings.value("speechRate", 1.0).toDouble();
    voiceSettings["pitch"] = settings.value("pitch", 1.0).toDouble();
    voiceSettings["volume"] = settings.value("volume", 1.0).toDouble();
    voiceSettings["language"] = settings.value("language", "en-US").toString();

    settings.endGroup();

    // Apply settings
    setVoiceSettings(voiceSettings);
}

void VoiceService::handleSpeechRecognized(const QString &text)
{
    emit speechRecognized(text);
}

void VoiceService::updateRecordingVolume()
{
    if (!m_isRecording || !m_audioInput) {
        return;
    }

    // Get the current volume level (0.0 to 1.0)
    m_recordingVolume = m_audioInput->volume();
    emit recordingVolumeChanged();
}

void VoiceService::handleSpeechStateChanged(QTextToSpeech::State state)
{
    switch (state) {
        case QTextToSpeech::Ready:
            m_isSpeaking = false;
            emit speakingStateChanged();
            emit speechFinished();
            break;
        case QTextToSpeech::Speaking:
            m_isSpeaking = true;
            emit speakingStateChanged();
            break;
        case QTextToSpeech::Paused:
            // Already handled by pauseSpeech()
            break;
        case QTextToSpeech::Synthesizing:
            // Preparing to speak, still consider as speaking
            m_isSpeaking = true;
            emit speakingStateChanged();
            break;
        case QTextToSpeech::Error:
            m_isSpeaking = false;
            emit speakingStateChanged();
            emit speechError("Text-to-speech error occurred");
            break;
    }
}

void VoiceService::initializeRecorder()
{
    // Create audio input
    m_audioInput = new QAudioInput(this);
    m_audioInput->setVolume(1.0);

    // Create media recorder
    m_recorder = new QMediaRecorder(this);

    // Set up capture session
    QMediaCaptureSession *captureSession = new QMediaCaptureSession(this);
    captureSession->setAudioInput(m_audioInput);
    captureSession->setRecorder(m_recorder);

    // Set up audio settings
    QMediaFormat format;
    format.setFileFormat(QMediaFormat::Wave);
    format.setAudioCodec(QMediaFormat::AudioCodec::Wave);
    m_recorder->setMediaFormat(format);

    // Set quality
    QAudioFormat audioFormat;
    audioFormat.setSampleRate(16000);
    audioFormat.setChannelCount(1);
    audioFormat.setSampleFormat(QAudioFormat::Int16);

    // Find the audio input device
    const QList<QAudioDevice> inputDevices = QMediaDevices::audioInputs();
    if (!inputDevices.isEmpty()) {
        m_audioInput->setDevice(inputDevices.first());
    }
}

void VoiceService::initializeTTS()
{
    m_tts = new QTextToSpeech(this);

    // Connect signals
    connect(m_tts, &QTextToSpeech::stateChanged, this, &VoiceService::handleSpeechStateChanged);

    // Set default values
    m_tts->setRate(1.0);
    m_tts->setPitch(1.0);
    m_tts->setVolume(1.0);

    // Set default voice if available
    if (!m_tts->availableVoices().isEmpty()) {
        m_tts->setVoice(m_tts->availableVoices().first());
        m_currentVoice = m_tts->availableVoices().first().name();
    }
}

void VoiceService::processSpeechRecognition(const QString &audioFilePath)
{
    // In a real implementation, this would send the audio file to a speech recognition service
    // For now, we'll just emit a placeholder text
    qDebug() << "Processing speech recognition for file:" << audioFilePath;

    // Simulate speech recognition with a delay
    QTimer::singleShot(1000, this, [this]() {
        emit speechRecognized("This is a simulated speech recognition result.");
    });

    // In a real implementation, you would use a service like Google Speech-to-Text, Azure Speech, etc.
    // Example pseudocode:
    /*
    QNetworkAccessManager *manager = new QNetworkAccessManager(this);
    QNetworkRequest request(QUrl("https://speech.googleapis.com/v1/speech:recognize"));
    request.setHeader(QNetworkRequest::ContentTypeHeader, "application/json");

    // Read audio file
    QFile file(audioFilePath);
    if (!file.open(QIODevice::ReadOnly)) {
        emit recordingError("Failed to open audio file for processing");
        return;
    }

    QByteArray audioData = file.readAll();
    file.close();

    // Encode audio data as base64
    QByteArray base64Audio = audioData.toBase64();

    // Create request body
    QJsonObject requestBody;
    QJsonObject config;
    config["encoding"] = "LINEAR16";
    config["sampleRateHertz"] = 16000;
    config["languageCode"] = m_currentLanguage;
    requestBody["config"] = config;

    QJsonObject audio;
    audio["content"] = QString(base64Audio);
    requestBody["audio"] = audio;

    QJsonDocument doc(requestBody);
    QByteArray jsonData = doc.toJson();

    // Send request
    QNetworkReply *reply = manager->post(request, jsonData);

    connect(reply, &QNetworkReply::finished, this, [this, reply]() {
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray responseData = reply->readAll();
            QJsonDocument doc = QJsonDocument::fromJson(responseData);
            QJsonObject obj = doc.object();

            // Parse response and extract recognized text
            QString recognizedText = obj["results"].toArray()[0].toObject()["alternatives"].toArray()[0].toObject()["transcript"].toString();

            emit speechRecognized(recognizedText);
        } else {
            emit recordingError("Speech recognition failed: " + reply->errorString());
        }

        reply->deleteLater();
    });
    */
}