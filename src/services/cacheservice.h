#ifndef CACHESERVICE_H
#define CACHESERVICE_H

#include "chamberservices_global.h"

#include <QObject>
#include "chamberservices_global.h"

#include <QCache>
#include "chamberservices_global.h"

#include <QString>
#include "chamberservices_global.h"

#include <QVariant>

class QTimer;

class CHAMBERSERVICES_EXPORT CacheService : public QObject
{
    Q_OBJECT
    Q_PROPERTY(int cacheSize READ cacheSize WRITE setCacheSize NOTIFY cacheSizeChanged)
    Q_PROPERTY(bool persistentCacheEnabled READ isPersistentCacheEnabled WRITE setPersistentCacheEnabled NOTIFY persistentCacheEnabledChanged)

public:
    explicit CacheService(QObject *parent = nullptr);
    
    void setCacheSize(int size);
    int cacheSize() const;
    
    bool contains(const QString &key) const;
    QVariant get(const QString &key) const;
    void insert(const QString &key, const QVariant &value);
    bool remove(const QString &key);
    void clear();
    
    void setPersistentCacheEnabled(bool enabled);
    bool isPersistentCacheEnabled() const;
    
    Q_INVOKABLE void saveCacheToDisk();
    Q_INVOKABLE void loadCacheFromDisk();
    Q_INVOKABLE void cleanupOldItems();

signals:
    void cacheSizeChanged();
    void persistentCacheEnabledChanged();

private:
    QCache<QString, QVariant> m_cache;
    bool m_persistentCacheEnabled;
    QTimer *m_autoSaveTimer;
    
    QString getCacheDir() const;
    qint64 getSystemMemoryMB() const;
    int estimateCost(const QVariant &value) const;
};

#endif // CACHESERVICE_H