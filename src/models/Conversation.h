#pragma once

#include <memory>

#include <QDateTime>
#include <QList>
#include <QString>

#include "Message.h"

namespace ChamberUI::Models {

struct Conversation {
  QString id;
  QString title;
  QDateTime created_at;
  QDateTime updated_at;
  QString provider;
  QString model;
  QString system_prompt;
  double temperature;
  int max_tokens;
  int message_count;
  QVariantMap settings;
  bool is_template;
  QString template_name;
  MessageListPtr messages;
  Conversation();
  Conversation(const QString &_id, const QString &_title);
  Conversation(const Conversation &) = delete;
};

using ConversationPtr = std::unique_ptr<Conversation>;
using ConversationListPtr = std::unique_ptr<QList<ConversationPtr>>;

} // namespace ChamberUI::Models