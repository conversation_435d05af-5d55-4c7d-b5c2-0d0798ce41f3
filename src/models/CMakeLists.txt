cmake_minimum_required(VERSION 3.16)

# 定义库名称
set(LIBRARY_NAME ChamberModels)

# 设置 C++ 标准
set(CMAKE_CXX_STANDARD 23)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 启用自动 MOC、RCC 和 UIC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTORCC ON)
set(CMAKE_AUTOUIC ON)

# 查找所需的 Qt 组件
find_package(Qt6 COMPONENTS Core REQUIRED)

# 收集所有源文件
file(GLOB_RECURSE SOURCES "*.cpp" "*.h")

# 创建库
add_library(${LIBRARY_NAME} STATIC ${SOURCES})

# 设置库的属性
set_target_properties(${LIBRARY_NAME} PROPERTIES
        VERSION 0.1
        SOVERSION 0.1
        PUBLIC_HEADER "${HEADERS}"
)

# 设置库的包含目录
target_include_directories(${LIBRARY_NAME}
        PUBLIC
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}>
        $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/..>  # 添加这一行
        $<INSTALL_INTERFACE:include>
)

# 链接 Qt 库
target_link_libraries(${LIBRARY_NAME}
        PUBLIC
        Qt6::Core
)

# 安装规则（可选）
install(TARGETS ${LIBRARY_NAME}
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib
        PUBLIC_HEADER DESTINATION include/${LIBRARY_NAME}
)
