#include "Conversation.h"

namespace ChamberUI::Models {

Conversation::Conversation()
    : created_at{QDateTime::currentDateTime()},
      updated_at{QDateTime::currentDateTime()}, provider{""}, model{""},
      system_prompt{""}, temperature{0.7}, max_tokens{2000}, is_template{false},
      template_name{""} {}

Conversation::Conversation(const QString &_id, const QString &_title)
    : id{_id}, title{_title}, created_at{QDateTime::currentDateTime()},
      updated_at{QDateTime::currentDateTime()}, provider{""}, model{""},
      system_prompt{""}, temperature{0.7}, max_tokens{2000}, is_template{false},
      template_name{""} {}

} // namespace ChamberUI::Models
