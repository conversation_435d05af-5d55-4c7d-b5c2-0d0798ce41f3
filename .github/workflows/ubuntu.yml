name: ubuntu

on: [push, pull_request]

permissions: read-all

jobs:
  ubuntu:
    strategy:
      fail-fast: false
      matrix:
        compiler:
        - { cc: "gcc-9",    cxx: "g++-9",      os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "gcc-10",   cxx: "g++-10",     os: "ubuntu-20.04", nonascii: "TRUE" }
        - { cc: "gcc-10",   cxx: "g++-10",     os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "gcc-11",   cxx: "g++-11",     os: "ubuntu-20.04", nonascii: "TRUE" }
        - { cc: "gcc-11",   cxx: "g++-11",     os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "gcc-12",   cxx: "g++-12",     os: "ubuntu-22.04", nonascii: "FALSE" }
        - { cc: "clang-9",  cxx: "clang++-9",  os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-10", cxx: "clang++-10", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-11", cxx: "clang++-11", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-12", cxx: "clang++-12", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-13", cxx: "clang++-13", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-14", cxx: "clang++-14", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-15", cxx: "clang++-15", os: "ubuntu-20.04", nonascii: "FALSE" }
        - { cc: "clang-16", cxx: "clang++-16", os: "ubuntu-20.04", nonascii: "FALSE" }

    name: "${{ format('{0} NONASCII={1}', matrix.compiler.cc, matrix.compiler.nonascii) }}"
    runs-on: ${{ matrix.compiler.os }}
    steps:
    - uses: actions/checkout@v3

    - name: Configure clang
      run: |
        if [[ "${{ matrix.compiler.cc }}" == "clang"* ]]; then
          wget -O - https://apt.llvm.org/llvm-snapshot.gpg.key|sudo apt-key add -
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-9 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-10 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-11 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-12 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-13 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-14 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-15 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal-16 main"
          sudo apt-add-repository "deb http://apt.llvm.org/focal/ llvm-toolchain-focal main"
          sudo apt update
          sudo apt install ${{ matrix.compiler.cc }} -y
        fi

    - name: Configure gcc
      run: |
        if [[ "${{ matrix.compiler.cc }}" == "gcc"* ]]; then
          sudo add-apt-repository ppa:ubuntu-toolchain-r/test -y
          sudo apt update
          sudo apt install ${{ matrix.compiler.cxx }} -y
        fi

    - name: Build Release
      run: |
        rm -rf build
        mkdir build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release -DCMAKE_CXX_COMPILER=${{ matrix.compiler.cxx }} -DMAGIC_ENUM_OPT_ENABLE_NONASCII:BOOL=${{ matrix.compiler.nonascii }}
        cmake --build . -j 4 --config Release
        ctest --output-on-failure -C Release

    - name: Build Debug
      run: |
        rm -rf build
        mkdir build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Debug -DCMAKE_CXX_COMPILER=${{ matrix.compiler.cxx }} -DMAGIC_ENUM_OPT_ENABLE_NONASCII:BOOL=${{ matrix.compiler.nonascii }}
        cmake --build . -j 4 --config Debug
        ctest --output-on-failure -C Debug

    - name: Bazel Test
      working-directory: test
      run: bazelisk test //... --config=ci
