name: macos

on: [push, pull_request]

permissions: read-all

jobs:
  build:
    runs-on: ${{ matrix.config.os }}
    strategy:
      fail-fast: false
      matrix:
        config:
        - { os: macos-11  } # https://github.com/actions/virtual-environments/blob/main/images/macos/macos-11-Readme.md#xcode
        - { os: macos-12 } # https://github.com/actions/virtual-environments/blob/main/images/macos/macos-12-Readme.md#xcode

    name: "${{ matrix.config.os }}"
    steps:
    - uses: actions/checkout@v3

    - name: Build Release
      run: |
        rm -rf build
        mkdir build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Release
        cmake --build . -j 4 --config Release
        ctest --output-on-failure -C Release

    - name: Build Debug
      run: |
        rm -rf build
        mkdir build
        cd build
        cmake .. -DCMAKE_BUILD_TYPE=Debug
        cmake --build . -j 4 --config Debug
        ctest --output-on-failure -C Debug

    - name: <PERSON>zel Test
      working-directory: test
      run: bazelisk test //... --config=ci
