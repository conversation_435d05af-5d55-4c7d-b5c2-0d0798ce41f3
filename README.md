# ChamberUI

ChamberUI is a modern macOS LLM chat application that supports multiple providers (OpenAI and Anthropic), file attachments, conversation management, and more.

## Features

- **Multiple LLM Providers**: Support for OpenAI and Anthropic models
- **File Attachments**: Attach images and documents to your messages
- **Conversation Management**: Save, organize, and search your conversations
- **Export/Import**: Export conversations to JSON, Markdown, HTML, or PDF
- **Voice Input/Output**: Record voice messages and listen to responses
- **Dark Mode**: Toggle between light and dark themes
- **Markdown Support**: Format messages with Markdown syntax
- **Code Highlighting**: Syntax highlighting for code blocks
- **Customizable Settings**: Adjust temperature, max tokens, and more

## Requirements

- macOS 10.15 or later
- Qt 6.2 or later
- CMake 3.16 or later
- C++17 compatible compiler

## Building from Source

### Prerequisites

1. Install Qt 6.2 or later from [qt.io](https://www.qt.io/download) or via Homebrew:
   ```
   brew install qt
   ```

2. Install CMake:
   ```
   brew install cmake
   ```

### Build Steps

1. Clone the repository:
   ```
   git clone https://github.com/soloholic/chamberui.git
   cd chamberui
   ```

2. Create a build directory:
   ```
   mkdir build
   cd build
   ```

3. Configure the project:
   ```
   cmake ..
   ```

4. Build the application:
   ```
   make
   ```

5. Run the application:
   ```
   open ChamberUI.app
   ```

## Creating a macOS App Bundle

To create a distributable macOS app bundle:

1. Build the application in Release mode:
   ```
   cmake -DCMAKE_BUILD_TYPE=Release ..
   make
   ```

2. Create the app bundle:
   ```
   make install
   ```

3. Create a DMG file for distribution:
   ```
   macdeployqt ChamberUI.app -dmg
   ```

## Usage

### API Keys

To use ChamberUI, you'll need API keys for the LLM providers:

1. For OpenAI: Get your API key from [OpenAI Platform](https://platform.openai.com/account/api-keys)
2. For Anthropic: Get your API key from [Anthropic Console](https://console.anthropic.com/account/keys)

Enter these keys in the Settings panel of the application.

### Keyboard Shortcuts

- **⌘ + N**: New conversation
- **⌘ + ,**: Open settings
- **⌘ + E**: Export conversation
- **⌘ + I**: Import conversation
- **⌘ + D**: Toggle dark mode
- **⌘ + F**: Search in conversation
- **⌘ + Delete**: Delete conversation
- **⌘ + S**: Save conversation settings
- **⌘ + R**: Regenerate last response
- **⌘ + ?**: Show help

## License

Copyright © 2025 Soloholic. All rights reserved.

## Support

For support, visit [soloholic.com/support](https://soloholic.com/support) <NAME_EMAIL>.