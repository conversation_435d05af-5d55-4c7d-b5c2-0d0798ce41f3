parameters:
  CXXVersions: [ 14, 17, 20 ]
  buildTypes: [ 'Debug', 'Release' ]
  image: ''

  compiler: ''
  compilerVersions: ["default"] # if default value, simply uses whatever version is on the machine.
                                # the text of this default value doesn't actually matter.
  setupfile: ''
  extraCmakeArgs: ''

jobs:
- ${{ each compilerVersion in parameters.compilerVersions }}:
  - ${{ each CXXVersion in parameters.CXXVersions }}:
    - ${{ each buildType in parameters.buildTypes }}:
      - job:
        displayName: ${{ format('{0} {1} C++{2} {3}', parameters.compiler, compilerVersion, CXXVersion, buildType) }}
        pool:
            vmImage: ${{ parameters.image }}
        continueOnError: false

        steps:
          - ${{ if not(eq(parameters.setupfile, '')) }}:
            - template: ${{ parameters.setupfile }}
              parameters:
                version: ${{ compilerVersion }}

          - task: CMake@1
            name: Configure
            inputs:
              workingDirectory: build
              cmakeArgs: '-DGSL_CXX_STANDARD=${{ CXXVersion }} -DCMAKE_BUILD_TYPE=${{ buildType }} -DCI_TESTING:BOOL=ON -DCMAKE_VERBOSE_MAKEFILE:BOOL=ON -Werror=dev ${{ parameters.extraCmakeArgs }} .. '

          - task: CMake@1
            name: Build
            inputs:
              workingDirectory: build
              cmakeArgs: '--build . '

          - script: ctest . --output-on-failure --no-compress-output
            name: CTest
            workingDirectory: build
            failOnStderr: true
