include(CheckCXXCompilerFlag)

set(CMAKE_CXX_STANDARD 17)
if((CMAKE_CXX_COMPILER_ID MATCHES "GNU") OR (CMAKE_CXX_COMPILER_ID MATCHES "Clang"))
    set(OPTIONS -Wall -Wextra -pedantic-errors -Werror)
elseif(CMAKE_CXX_COMPILER_ID MATCHES "MSVC")
    set(OPTIONS /W4 /WX)
    if(HAS_PERMISSIVE_FLAG)
        set(OPTIONS ${OPTIONS} /permissive-)
    endif()
endif()

function(make_example target)
    add_executable(${target} ${target}.cpp)
    set_target_properties(${target} PROPERTIES CXX_EXTENSIONS OFF)
    target_compile_features(${target} PRIVATE cxx_std_17)
    target_compile_options(${target} PRIVATE ${OPTIONS})
    target_link_libraries(${target} PRIVATE ${CMAKE_PROJECT_NAME})
endfunction()

make_example(example)
make_example(enum_flag_example)
make_example(example_containers_array)
make_example(example_containers_bitset)
make_example(example_containers_set)
make_example(example_custom_name)
make_example(example_switch)
if(MAGIC_ENUM_OPT_ENABLE_NONASCII)
    make_example(example_nonascii_name)
endif()
