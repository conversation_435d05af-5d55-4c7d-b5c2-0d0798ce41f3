# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands
- Build project: `cmake -DCMAKE_BUILD_TYPE=Release .. && make`
- Generate app bundle: `make install`
- Create distributable: `macdeployqt ChamberUI.app -dmg`
- Run the application: `open ChamberUI.app`

## Code Style Guidelines
- **C++ Naming**: Classes use PascalCase, methods use camelCase, member variables use camelCase with m_ prefix, static variables use s_ prefix
- **QML Naming**: Component IDs use camelCase, properties use camelCase, signal handlers use onActionName format
- **Imports**: C++ imports ordered as standard libraries, Qt libraries, then local includes; QML imports ordered as QtQuick modules followed by custom components
- **Qt Patterns**: Use Q_PROPERTY for properties, signals/slots for communication, and MVVM architecture pattern
- **Error Handling**: Use QDebug for logging, signal-based error notifications, and clear error messages in UI components
- **Documentation**: Document public methods and non-obvious implementations
- **Services**: Implement as singletons with clear responsibility boundaries
- **UI Components**: Follow existing component patterns with proper anchoring and layouts

The codebase follows a Model-View-ViewModel (MVVM) architecture with Qt/QML for UI components, C++ for business logic, and SQLite for data persistence.