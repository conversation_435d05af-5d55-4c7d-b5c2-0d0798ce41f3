import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "../styles"

Rectangle {
    id: updateNotification
    width: parent.width
    height: visible ? contentLayout.height + 32 : 0
    color: root.secondaryColor
    visible: false
    
    property string version: ""
    property string releaseNotes: ""
    
    // Signals
    signal downloadRequested()
    signal skipRequested()
    signal remindLaterRequested()
    
    // Animation for showing/hiding
    Behavior on height {
        NumberAnimation { duration: 300; easing.type: Easing.OutQuad }
    }
    
    // Top border
    Rectangle {
        width: parent.width
        height: 1
        color: root.borderColor
        anchors.top: parent.top
    }
    
    // Bottom border
    Rectangle {
        width: parent.width
        height: 1
        color: root.borderColor
        anchors.bottom: parent.bottom
    }
    
    // Content
    ColumnLayout {
        id: contentLayout
        anchors {
            left: parent.left
            right: parent.right
            top: parent.top
            margins: 16
        }
        spacing: 12
        
        // Header with version and close button
        RowLayout {
            Layout.fillWidth: true
            spacing: 8
            
            Label {
                text: "Update Available: v" + version
                font.pixelSize: 16
                font.weight: Font.Bold
                color: root.textColor
                Layout.fillWidth: true
            }
            
            Button {
                text: "×"
                font.pixelSize: 18
                onClicked: {
                    updateNotification.skipRequested()
                    updateNotification.visible = false
                }
                
                background: Rectangle {
                    color: "transparent"
                    radius: 4
                }
            }
        }
        
        // Release notes
        Label {
            text: releaseNotes
            wrapMode: Text.WordWrap
            color: root.textColor
            Layout.fillWidth: true
            Layout.maximumHeight: 100
            clip: true
            
            // Add scrolling if the text is too long
            MouseArea {
                anchors.fill: parent
                onWheel: function(wheel) {
                    if (parent.contentHeight > parent.height) {
                        parent.contentY += wheel.angleDelta.y / 2
                        if (parent.contentY < 0) parent.contentY = 0
                        if (parent.contentY > parent.contentHeight - parent.height)
                            parent.contentY = parent.contentHeight - parent.height
                    }
                }
            }
        }
        
        // Action buttons
        RowLayout {
            Layout.fillWidth: true
            spacing: 8
            
            Button {
                text: "Download Now"
                Layout.preferredWidth: 150
                onClicked: {
                    updateNotification.downloadRequested()
                    updateNotification.visible = false
                }
            }
            
            Button {
                text: "Remind Later"
                Layout.preferredWidth: 150
                onClicked: {
                    updateNotification.remindLaterRequested()
                    updateNotification.visible = false
                }
            }
            
            Item { Layout.fillWidth: true }
            
            Button {
                text: "Skip This Version"
                Layout.preferredWidth: 150
                onClicked: {
                    updateNotification.skipRequested()
                    updateNotification.visible = false
                }
            }
        }
    }
    
    // Show the notification
    function showNotification(newVersion, notes) {
        version = newVersion
        releaseNotes = notes
        visible = true
    }
}