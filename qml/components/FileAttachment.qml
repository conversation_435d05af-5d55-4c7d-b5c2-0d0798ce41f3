import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1

Rectangle {
    id: fileAttachment

    // Properties
    property string filename: ""
    property string filePath: ""
    property string fileType: "other" // "image", "pdf", "text", "other"
    property string mimeType: ""
    property int fileSize: 0
    property bool isPreview: false // Whether this is a preview before sending
    property bool canRemove: false // Whether the attachment can be removed

    signal removeClicked()

    // Computed properties
    property string fileSizeText: {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return Math.round(fileSize / 1024) + " KB";
        } else {
            return Math.round(fileSize / (1024 * 1024) * 10) / 10 + " MB";
        }
    }

    property string fileIcon: {
        switch (fileType) {
            case "image": return "qrc:/icons/image.svg";
            case "pdf": return "qrc:/icons/pdf.svg";
            case "text": return "qrc:/icons/text.svg";
            default: return "qrc:/icons/file.svg";
        }
    }

    width: parent.width
    height: fileType === "image" ? imageViewer.height + 40 : 80
    color: root.secondaryColor
    radius: 8
    border.width: 1
    border.color: root.borderColor

    ColumnLayout {
        anchors {
            fill: parent
            margins: 8
        }
        spacing: 8

        // Image preview
        ImageViewer {
            id: imageViewer
            source: fileType === "image" ? filePath : ""
            visible: fileType === "image"
            Layout.alignment: Qt.AlignCenter
        }

        // File info row
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            // File icon
            Rectangle {
                width: 40
                height: 40
                color: "transparent"
                visible: fileType !== "image" || !imageViewer.visible

                Image {
                    anchors.centerIn: parent
                    width: 32
                    height: 32
                    source: fileIcon
                    sourceSize.width: 32
                    sourceSize.height: 32
                }
            }

            // File details
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 2

                // Filename
                Label {
                    text: filename
                    font.pixelSize: 14
                    elide: Text.ElideMiddle
                    Layout.fillWidth: true
                }

                // File size
                Label {
                    text: fileSizeText
                    font.pixelSize: 12
                    color: root.mutedTextColor
                }
            }

            // Action buttons
            RowLayout {
                spacing: 4

                // Open button
                Button {
                    text: "Open"
                    onClicked: {
                        Qt.openUrlExternally(filePath);
                    }
                }

                // Remove button (only shown in preview mode)
                Button {
                    text: "×"
                    visible: canRemove
                    onClicked: {
                        fileAttachment.removeClicked();
                    }
                }
            }
        }
    }

    // Context menu
    MouseArea {
        anchors.fill: parent
        acceptedButtons: Qt.RightButton

        onClicked: {
            if (mouse.button === Qt.RightButton) {
                contextMenu.popup();
            }
        }
    }

    Menu {
        id: contextMenu

        MenuItem {
            text: "Open"
            onTriggered: {
                Qt.openUrlExternally(filePath);
            }
        }

        MenuItem {
            text: "Save As..."
            onTriggered: {
                saveFileDialog.open();
            }
        }

        MenuItem {
            text: "Copy Path"
            onTriggered: {
                clipboard.text = filePath;
            }
        }
    }

    // File dialog for saving
    FileDialog {
        id: saveFileDialog
        title: "Save File"
        fileMode: FileDialog.SaveFile
        defaultSuffix: {
            var parts = filename.split('.');
            return parts.length > 1 ? parts[parts.length - 1] : "";
        }

        onAccepted: {
            // TODO: Implement file saving
            console.log("Save file to:", saveFileDialog.file);
        }
    }

    // Copy functionality using standard methods
    function copyToClipboard(text) {
        // Use a TextEdit as a clipboard workaround
        var textEdit = Qt.createQmlObject('import QtQuick 2.15; TextEdit { visible: false }', fileAttachment, "clipboard");
        textEdit.text = text;
        textEdit.selectAll();
        textEdit.copy();
        textEdit.destroy();
    }
}