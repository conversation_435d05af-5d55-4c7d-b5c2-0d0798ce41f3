import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1

Rectangle {
    id: imageViewer
    
    // Properties
    property string source: ""
    property bool isFullscreen: false
    property real maxPreviewWidth: 300
    property real maxPreviewHeight: 200
    
    // Computed properties
    property real imageRatio: imageLoader.status === Image.Ready ? imageLoader.sourceSize.width / imageLoader.sourceSize.height : 1.0
    property real displayWidth: isFullscreen ? Math.min(imageLoader.sourceSize.width, parent.width) : 
                                Math.min(imageLoader.sourceSize.width, maxPreviewWidth)
    property real displayHeight: isFullscreen ? Math.min(imageLoader.sourceSize.height, parent.height) : 
                                 Math.min(imageLoader.sourceSize.height, maxPreviewHeight)
    
    color: "transparent"
    width: displayWidth
    height: displayHeight
    
    // Image loader (hidden, used to get image properties)
    Image {
        id: imageLoader
        source: imageViewer.source
        visible: false
        asynchronous: true
        
        onStatusChanged: {
            if (status === Image.Ready) {
                // Adjust the size of the container based on the image dimensions
                if (!isFullscreen) {
                    if (sourceSize.width > maxPreviewWidth || sourceSize.height > maxPreviewHeight) {
                        if (imageRatio > 1) { // Wider than tall
                            imageViewer.width = maxPreviewWidth
                            imageViewer.height = maxPreviewWidth / imageRatio
                        } else { // Taller than wide
                            imageViewer.height = maxPreviewHeight
                            imageViewer.width = maxPreviewHeight * imageRatio
                        }
                    } else {
                        imageViewer.width = sourceSize.width
                        imageViewer.height = sourceSize.height
                    }
                }
            }
        }
    }
    
    // Actual displayed image
    Rectangle {
        id: imageContainer
        anchors.fill: parent
        color: "transparent"
        
        Image {
            id: displayImage
            anchors.fill: parent
            source: imageViewer.source
            fillMode: Image.PreserveAspectFit
            asynchronous: true
            cache: true
            
            // Show loading indicator while image is loading
            BusyIndicator {
                anchors.centerIn: parent
                running: displayImage.status === Image.Loading
                visible: running
            }
        }
        
        // Error message if image fails to load
        Text {
            anchors.centerIn: parent
            text: "Failed to load image"
            color: "#FF0000"
            visible: displayImage.status === Image.Error
        }
        
        // Mouse area for handling clicks
        MouseArea {
            anchors.fill: parent
            onClicked: {
                if (!isFullscreen) {
                    // Open fullscreen viewer
                    fullscreenLoader.active = true
                }
            }
        }
    }
    
    // Fullscreen viewer (loaded on demand)
    Loader {
        id: fullscreenLoader
        active: false
        sourceComponent: Rectangle {
            id: fullscreenViewer
            anchors.fill: parent
            color: Qt.rgba(0, 0, 0, 0.9)
            z: 1000 // Ensure it's above everything else
            
            // Fullscreen image
            Image {
                id: fullscreenImage
                anchors.centerIn: parent
                width: Math.min(sourceSize.width, parent.width * 0.9)
                height: Math.min(sourceSize.height, parent.height * 0.9)
                source: imageViewer.source
                fillMode: Image.PreserveAspectFit
                
                // Pinch area for zooming
                PinchArea {
                    anchors.fill: parent
                    pinch.target: fullscreenImage
                    pinch.minimumScale: 0.5
                    pinch.maximumScale: 3.0
                    
                    // Mouse area for panning and closing
                    MouseArea {
                        anchors.fill: parent
                        drag.target: fullscreenImage
                        
                        onDoubleClicked: {
                            fullscreenLoader.active = false
                        }
                    }
                }
            }
            
            // Close button
            Button {
                anchors {
                    top: parent.top
                    right: parent.right
                    margins: 20
                }
                text: "×"
                font.pixelSize: 24
                onClicked: {
                    fullscreenLoader.active = false
                }
            }
            
            // Save button
            Button {
                anchors {
                    bottom: parent.bottom
                    right: parent.right
                    margins: 20
                }
                text: "Save"
                onClicked: {
                    fileDialog.open()
                }
            }
            
            // File dialog for saving
            FileDialog {
                id: fileDialog
                title: "Save Image"
                fileMode: FileDialog.SaveFile
                nameFilters: ["Image files (*.jpg *.jpeg *.png *.gif)"]
                
                onAccepted: {
                    // TODO: Implement file saving
                    console.log("Save image to:", fileDialog.file)
                }
            }
        }
    }
}