import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: attachmentPreview
    
    // Properties
    property var attachments: [] // Array of attachment objects with {path, filename, type, size}
    
    // Signals
    signal removeAttachment(int index)
    
    color: "transparent"
    height: attachmentsRepeater.count > 0 ? attachmentsColumn.height + 16 : 0
    visible: attachmentsRepeater.count > 0
    
    ColumnLayout {
        id: attachmentsColumn
        anchors {
            left: parent.left
            right: parent.right
            top: parent.top
            margins: 8
        }
        spacing: 8
        
        // Header
        RowLayout {
            Layout.fillWidth: true
            
            Label {
                text: "Attachments (" + attachmentsRepeater.count + ")"
                font.pixelSize: 14
                font.weight: Font.Medium
                color: root.textColor
            }
            
            Item { Layout.fillWidth: true }
            
            Button {
                text: "Clear All"
                visible: attachmentsRepeater.count > 1
                onClicked: {
                    // Remove all attachments
                    attachments = []
                }
            }
        }
        
        // Attachments list
        Repeater {
            id: attachmentsRepeater
            model: attachments
            
            delegate: FileAttachment {
                Layout.fillWidth: true
                filename: modelData.filename
                filePath: modelData.path
                fileType: modelData.type
                fileSize: modelData.size || 0
                isPreview: true
                canRemove: true
                
                onRemoveClicked: {
                    attachmentPreview.removeAttachment(index)
                }
            }
        }
    }
}