import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: textToSpeech
    width: 300
    height: 80
    radius: 8
    color: root.secondaryColor
    border.width: 1
    border.color: root.borderColor
    
    // Properties
    property bool isVisible: false
    property bool isSpeaking: voiceService.isSpeaking
    property string text: ""
    
    // Signals
    signal closed()
    
    // Visibility
    visible: isVisible
    opacity: isVisible ? 1.0 : 0.0
    
    Behavior on opacity {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutQuad
        }
    }
    
    // Layout
    ColumnLayout {
        anchors.fill: parent
        anchors.margins: 12
        spacing: 8
        
        // Title and status
        RowLayout {
            Layout.fillWidth: true
            spacing: 8
            
            // Title
            Label {
                text: "Text-to-Speech"
                font.pixelSize: 14
                font.weight: Font.DemiBold
                color: root.textColor
                Layout.fillWidth: true
            }
            
            // Status
            Label {
                text: isSpeaking ? "Speaking..." : "Ready"
                font.pixelSize: 12
                color: root.mutedTextColor
            }
        }
        
        // Controls
        RowLayout {
            Layout.fillWidth: true
            spacing: 8
            
            // Play/Pause button
            Button {
                id: playPauseButton
                implicitWidth: 36
                implicitHeight: 36
                
                // Icon
                contentItem: Text {
                    text: isSpeaking ? "⏸" : "▶"
                    font.pixelSize: 16
                    color: root.textColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: {
                    if (isSpeaking) {
                        voiceService.pauseSpeech()
                    } else {
                        if (text.length > 0) {
                            voiceService.speak(text)
                        }
                    }
                }
            }
            
            // Stop button
            Button {
                id: stopButton
                implicitWidth: 36
                implicitHeight: 36
                enabled: isSpeaking
                
                // Icon
                contentItem: Text {
                    text: "⏹"
                    font.pixelSize: 16
                    color: stopButton.enabled ? root.textColor : root.mutedTextColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: {
                    voiceService.stopSpeech()
                    textToSpeech.isVisible = false
                    textToSpeech.closed()
                }
            }
            
            // Close button
            Button {
                id: closeButton
                implicitWidth: 36
                implicitHeight: 36
                
                // Icon
                contentItem: Text {
                    text: "✕"
                    font.pixelSize: 16
                    color: root.textColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }
                
                onClicked: {
                    if (isSpeaking) {
                        voiceService.stopSpeech()
                    }
                    textToSpeech.isVisible = false
                    textToSpeech.closed()
                }
            }
        }
    }
    
    // Handle speech events
    Connections {
        target: voiceService
        
        function onSpeechFinished() {
            textToSpeech.isVisible = false
            textToSpeech.closed()
        }
        
        function onSpeechError(error) {
            // Show error message
            console.error("Speech error:", error)
            textToSpeech.isVisible = false
            textToSpeech.closed()
        }
    }
}