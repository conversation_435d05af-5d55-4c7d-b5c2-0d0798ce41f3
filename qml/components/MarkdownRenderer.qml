import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1

Item {
    id: markdownRenderer
    width: parent.width
    height: contentColumn.height
    
    // Properties
    property string text: ""
    property color textColor: "black"
    property color linkColor: "#0366d6"
    property color codeBackgroundColor: "#f6f8fa"
    property color blockquoteColor: "#e0e0e0"
    property color tableBorderColor: "#dfe2e5"
    property int fontSize: 14
    property bool copyEnabled: true
    
    // Signals
    signal linkActivated(string link)
    
    // Parse markdown and generate components
    function parseMarkdown(markdown) {
        // Clear previous content
        for (let i = contentColumn.children.length - 1; i >= 0; i--) {
            contentColumn.children[i].destroy();
        }
        
        if (!markdown) return;
        
        // Split into lines for processing
        const lines = markdown.split('\n');
        let i = 0;
        
        while (i < lines.length) {
            const line = lines[i];
            
            // Check for code blocks
            if (line.trim().startsWith('```')) {
                const language = line.trim().substring(3).trim();
                let codeContent = [];
                i++;
                
                // Collect all lines until closing ```
                while (i < lines.length && !lines[i].trim().startsWith('```')) {
                    codeContent.push(lines[i]);
                    i++;
                }
                
                // Create code block component
                createCodeBlock(codeContent.join('\n'), language);
                i++; // Skip closing ```
                continue;
            }
            
            // Check for tables
            if (line.includes('|') && i + 1 < lines.length && lines[i + 1].includes('|') && 
                lines[i + 1].replace(/[^|]/g, '').length >= line.replace(/[^|]/g, '').length) {
                
                let tableRows = [line];
                i++;
                
                // Skip header separator
                if (lines[i].includes('|') && lines[i].replace(/[^|\-:]/g, '').length === lines[i].trim().length) {
                    tableRows.push(lines[i]);
                    i++;
                }
                
                // Collect table rows
                while (i < lines.length && lines[i].includes('|')) {
                    tableRows.push(lines[i]);
                    i++;
                }
                
                createTable(tableRows);
                continue;
            }
            
            // Check for blockquotes
            if (line.trim().startsWith('>')) {
                let quoteContent = [line.substring(line.indexOf('>') + 1).trim()];
                i++;
                
                // Collect all lines of the blockquote
                while (i < lines.length && lines[i].trim().startsWith('>')) {
                    quoteContent.push(lines[i].substring(lines[i].indexOf('>') + 1).trim());
                    i++;
                }
                
                createBlockquote(quoteContent.join('\n'));
                continue;
            }
            
            // Check for horizontal rule
            if (line.trim().match(/^(\*{3,}|-{3,}|_{3,})$/)) {
                createHorizontalRule();
                i++;
                continue;
            }
            
            // Check for headings
            if (line.trim().startsWith('#')) {
                const match = line.trim().match(/^(#{1,6})\s+(.+)$/);
                if (match) {
                    const level = match[1].length;
                    const headingText = match[2];
                    createHeading(headingText, level);
                    i++;
                    continue;
                }
            }
            
            // Check for lists
            if (line.trim().match(/^(\*|\-|\+|\d+\.)\s/)) {
                let listItems = [];
                let isList = true;
                let isOrdered = line.trim().match(/^\d+\.\s/) !== null;
                
                while (i < lines.length && isList) {
                    const currentLine = lines[i].trim();
                    if (currentLine.match(/^(\*|\-|\+|\d+\.)\s/)) {
                        // Extract the list item text (remove the bullet/number)
                        const itemText = currentLine.replace(/^(\*|\-|\+|\d+\.)\s/, '');
                        listItems.push(itemText);
                        i++;
                    } else if (currentLine === '') {
                        // Empty line might be a separator between list items
                        i++;
                    } else {
                        isList = false;
                    }
                }
                
                if (listItems.length > 0) {
                    createList(listItems, isOrdered);
                    continue;
                }
            }
            
            // Regular paragraph
            let paragraphLines = [line];
            i++;
            
            while (i < lines.length && lines[i].trim() !== '' && 
                   !lines[i].trim().startsWith('#') && 
                   !lines[i].trim().startsWith('```') && 
                   !lines[i].trim().startsWith('>') && 
                   !lines[i].trim().match(/^(\*{3,}|-{3,}|_{3,})$/) &&
                   !lines[i].trim().match(/^(\*|\-|\+|\d+\.)\s/) &&
                   !lines[i].includes('|')) {
                paragraphLines.push(lines[i]);
                i++;
            }
            
            createParagraph(paragraphLines.join(' '));
        }
    }
    
    // Create a paragraph with inline formatting
    function createParagraph(text) {
        const component = Qt.createComponent("qrc:/qml/components/MarkdownText.qml");
        if (component.status === Component.Ready) {
            const textObj = component.createObject(contentColumn, {
                "width": Qt.binding(function() { return contentColumn.width; }),
                "text": formatInlineMarkdown(text),
                "textColor": markdownRenderer.textColor,
                "linkColor": markdownRenderer.linkColor,
                "fontSize": markdownRenderer.fontSize
            });
            
            textObj.linkActivated.connect(function(link) {
                markdownRenderer.linkActivated(link);
            });
        }
    }
    
    // Create a heading
    function createHeading(text, level) {
        const component = Qt.createComponent("qrc:/qml/components/MarkdownText.qml");
        if (component.status === Component.Ready) {
            const textObj = component.createObject(contentColumn, {
                "width": Qt.binding(function() { return contentColumn.width; }),
                "text": formatInlineMarkdown(text),
                "textColor": markdownRenderer.textColor,
                "linkColor": markdownRenderer.linkColor,
                "fontSize": markdownRenderer.fontSize + (6 - level) * 2,
                "font.weight": Font.Bold,
                "topPadding": 8,
                "bottomPadding": 4
            });
            
            textObj.linkActivated.connect(function(link) {
                markdownRenderer.linkActivated(link);
            });
        }
    }
    
    // Create a code block with syntax highlighting
    function createCodeBlock(code, language) {
        const component = Qt.createComponent("qrc:/qml/components/CodeBlock.qml");
        if (component.status === Component.Ready) {
            const codeBlock = component.createObject(contentColumn, {
                "width": Qt.binding(function() { return contentColumn.width; }),
                "code": code,
                "language": language,
                "backgroundColor": codeBackgroundColor,
                "textColor": markdownRenderer.textColor,
                "fontSize": markdownRenderer.fontSize,
                "copyEnabled": markdownRenderer.copyEnabled
            });
        }
    }
    
    // Create a blockquote
    function createBlockquote(text) {
        const component = Qt.createComponent("qrc:/qml/components/MarkdownText.qml");
        if (component.status === Component.Ready) {
            const container = Qt.createQmlObject(`
                import QtQuick 2.15
                Rectangle {
                    width: parent.width
                    height: childrenRect.height + 16
                    color: "transparent"
                    
                    Rectangle {
                        width: 4
                        height: parent.height
                        color: "${blockquoteColor}"
                        anchors.left: parent.left
                        anchors.leftMargin: 0
                    }
                }
            `, contentColumn);
            
            const textObj = component.createObject(container, {
                "width": Qt.binding(function() { return container.width - 16; }),
                "anchors.left": "parent.left",
                "anchors.leftMargin": 16,
                "anchors.top": "parent.top",
                "anchors.topMargin": 8,
                "text": formatInlineMarkdown(text),
                "textColor": Qt.rgba(markdownRenderer.textColor.r, markdownRenderer.textColor.g, markdownRenderer.textColor.b, 0.8),
                "linkColor": markdownRenderer.linkColor,
                "fontSize": markdownRenderer.fontSize,
                "font.italic": true
            });
            
            textObj.linkActivated.connect(function(link) {
                markdownRenderer.linkActivated(link);
            });
        }
    }
    
    // Create a horizontal rule
    function createHorizontalRule() {
        const component = Qt.createQmlObject(`
            import QtQuick 2.15
            Rectangle {
                width: parent.width
                height: 1
                color: Qt.rgba(0, 0, 0, 0.1)
                anchors.horizontalCenter: parent.horizontalCenter
                
                Rectangle {
                    width: parent.width
                    height: 16
                    color: "transparent"
                    anchors.centerIn: parent
                }
            }
        `, contentColumn);
    }
    
    // Create a list (ordered or unordered)
    function createList(items, ordered) {
        const listContainer = Qt.createQmlObject(`
            import QtQuick 2.15
            import QtQuick.Layouts 1.15
            ColumnLayout {
                width: parent.width
                spacing: 4
            }
        `, contentColumn);
        
        const textComponent = Qt.createComponent("qrc:/qml/components/MarkdownText.qml");
        
        for (let i = 0; i < items.length; i++) {
            const itemContainer = Qt.createQmlObject(`
                import QtQuick 2.15
                import QtQuick.Layouts 1.15
                RowLayout {
                    width: parent.width
                    spacing: 8
                }
            `, listContainer);
            
            // Create bullet or number
            const bulletText = ordered ? (i + 1) + "." : "•";
            const bullet = Qt.createQmlObject(`
                import QtQuick 2.15
                import QtQuick.Controls 2.15
                Label {
                    text: "${bulletText}"
                    color: "${markdownRenderer.textColor}"
                    font.pixelSize: ${markdownRenderer.fontSize}
                    Layout.preferredWidth: ${ordered ? 20 : 15}
                }
            `, itemContainer);
            
            // Create item text with inline formatting
            if (textComponent.status === Component.Ready) {
                const textObj = textComponent.createObject(itemContainer, {
                    "Layout.fillWidth": true,
                    "text": formatInlineMarkdown(items[i]),
                    "textColor": markdownRenderer.textColor,
                    "linkColor": markdownRenderer.linkColor,
                    "fontSize": markdownRenderer.fontSize
                });
                
                textObj.linkActivated.connect(function(link) {
                    markdownRenderer.linkActivated(link);
                });
            }
        }
    }
    
    // Create a table
    function createTable(rows) {
        // Parse table structure
        const tableData = rows.map(row => {
            // Split by pipe, but ignore escaped pipes
            const cells = [];
            let currentCell = "";
            let isEscaped = false;
            
            for (let i = 0; i < row.length; i++) {
                const currentChar = row[i];
                
                if (currentChar === '\\' && !isEscaped) {
                    isEscaped = true;
                    continue;
                }
                
                if (currentChar === '|' && !isEscaped) {
                    cells.push(currentCell.trim());
                    currentCell = "";
                } else {
                    currentCell += currentChar;
                }
                
                isEscaped = false;
            }
            
            // Add the last cell if there's content
            if (currentCell.trim()) {
                cells.push(currentCell.trim());
            }
            
            // Remove empty cells at the beginning and end (caused by leading/trailing pipes)
            if (cells.length > 0 && cells[0] === "") cells.shift();
            if (cells.length > 0 && cells[cells.length - 1] === "") cells.pop();
            
            return cells;
        });
        
        // Check if second row is a separator row
        let hasHeader = false;
        let alignments = [];
        
        if (tableData.length > 1 && tableData[1].every(cell => cell.match(/^[\s\-:]+$/))) {
            hasHeader = true;
            alignments = tableData[1].map(cell => {
                if (cell.startsWith(':') && cell.endsWith(':')) return Text.AlignHCenter;
                if (cell.endsWith(':')) return Text.AlignRight;
                return Text.AlignLeft;
            });
            
            // Remove the separator row
            tableData.splice(1, 1);
        } else {
            // Default alignments (left)
            alignments = tableData[0].map(() => Text.AlignLeft);
        }
        
        // Create table component
        const tableContainer = Qt.createQmlObject(`
            import QtQuick 2.15
            Rectangle {
                width: parent.width
                height: childrenRect.height + 1
                color: "transparent"
                border.color: "${tableBorderColor}"
                border.width: 1
                radius: 3
            }
        `, contentColumn);
        
        const tableGrid = Qt.createQmlObject(`
            import QtQuick 2.15
            import QtQuick.Layouts 1.15
            GridLayout {
                anchors.top: parent.top
                anchors.left: parent.left
                anchors.right: parent.right
                anchors.margins: 1
                columns: ${tableData[0].length}
                rowSpacing: 0
                columnSpacing: 0
            }
        `, tableContainer);
        
        const textComponent = Qt.createComponent("qrc:/qml/components/MarkdownText.qml");
        
        // Create cells
        for (let rowIndex = 0; rowIndex < tableData.length; rowIndex++) {
            const row = tableData[rowIndex];
            const isHeaderRow = hasHeader && rowIndex === 0;
            
            for (let colIndex = 0; colIndex < row.length; colIndex++) {
                const cellContainer = Qt.createQmlObject(`
                    import QtQuick 2.15
                    Rectangle {
                        Layout.fillWidth: true
                        Layout.preferredHeight: cellContent.height + 16
                        color: ${isHeaderRow ? "Qt.rgba(0, 0, 0, 0.03)" : "\"transparent\""}
                        border.color: "${tableBorderColor}"
                        border.width: ${rowIndex > 0 && colIndex > 0 ? 1 : 0}
                        border.topWidth: ${rowIndex > 0 ? 1 : 0}
                        border.leftWidth: ${colIndex > 0 ? 1 : 0}
                    }
                `, tableGrid);
                
                if (textComponent.status === Component.Ready) {
                    const cellText = row[colIndex] || "";
                    const textObj = textComponent.createObject(cellContainer, {
                        "id": "cellContent",
                        "width": Qt.binding(function() { return cellContainer.width - 16; }),
                        "anchors.centerIn": "parent",
                        "horizontalAlignment": alignments[colIndex],
                        "text": formatInlineMarkdown(cellText),
                        "textColor": markdownRenderer.textColor,
                        "linkColor": markdownRenderer.linkColor,
                        "fontSize": markdownRenderer.fontSize,
                        "font.weight": isHeaderRow ? Font.Bold : Font.Normal
                    });
                    
                    textObj.linkActivated.connect(function(link) {
                        markdownRenderer.linkActivated(link);
                    });
                }
            }
        }
    }
    
    // Format inline markdown (bold, italic, links, inline code)
    function formatInlineMarkdown(text) {
        if (!text) return "";
        
        let formattedText = text;
        
        // Escape HTML special characters
        formattedText = formattedText.replace(/&/g, "&amp;")
                                    .replace(/</g, "&lt;")
                                    .replace(/>/g, "&gt;");
        
        // Format inline code
        formattedText = formattedText.replace(/`([^`]+)`/g, "<code style='background-color:" + 
                                             codeBackgroundColor + "; padding: 2px 4px; border-radius: 3px;'>$1</code>");
        
        // Format bold (both ** and __ syntax)
        formattedText = formattedText.replace(/\*\*([^*]+)\*\*/g, "<b>$1</b>");
        formattedText = formattedText.replace(/__([^_]+)__/g, "<b>$1</b>");
        
        // Format italic (both * and _ syntax)
        formattedText = formattedText.replace(/\*([^*]+)\*/g, "<i>$1</i>");
        formattedText = formattedText.replace(/_([^_]+)_/g, "<i>$1</i>");
        
        // Format links
        formattedText = formattedText.replace(/\[([^\]]+)\]\(([^)]+)\)/g, 
                                             "<a href='$2' style='color:" + linkColor + ";'>$1</a>");
        
        // Format URLs (auto-linking)
        const urlRegex = /(\b(https?|ftp):\/\/[-A-Z0-9+&@#\/%?=~_|!:,.;]*[-A-Z0-9+&@#\/%=~_|])/gi;
        formattedText = formattedText.replace(urlRegex, "<a href='$1' style='color:" + linkColor + ";'>$1</a>");
        
        return formattedText;
    }
    
    // Main content column
    ColumnLayout {
        id: contentColumn
        width: parent.width
        spacing: 8
    }
    
    // Update content when text changes
    onTextChanged: {
        parseMarkdown(text);
    }
    
    // Handle link activation
    function openLink(link) {
        Qt.openUrlExternally(link);
    }
    
    Component.onCompleted: {
        parseMarkdown(text);
    }
}