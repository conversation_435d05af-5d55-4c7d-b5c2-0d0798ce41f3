import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "."

Rectangle {
    id: newConversationDialog
    color: Qt.rgba(0, 0, 0, 0.5)
    visible: false

    // Properties
    property var templates: []

    // Signals
    signal closed()
    signal conversationCreated(string title, string provider, string model, string systemPrompt, real temperature, int maxTokens)

    // Visibility
    opacity: visible ? 1 : 0

    Behavior on opacity {
        NumberAnimation {
            duration: 200
            easing.type: Easing.OutQuad
        }
    }

    // Close when clicking outside the dialog
    MouseArea {
        anchors.fill: parent
        onClicked: {
            newConversationDialog.visible = false
            newConversationDialog.closed()
        }
    }

    // Dialog content
    Rectangle {
        id: dialogContent
        width: Math.min(parent.width - 40, 500)
        height: Math.min(parent.height - 40, contentLayout.implicitHeight + 40)
        anchors.centerIn: parent
        color: root.primaryColor
        radius: 8

        // Prevent clicks from propagating to the background
        MouseArea {
            anchors.fill: parent
            onClicked: mouse.accepted = true
        }

        ColumnLayout {
            id: contentLayout
            anchors {
                fill: parent
                margins: 20
            }
            spacing: 16

            // Header
            Label {
                text: "New Conversation"
                font.pixelSize: 24
                font.weight: Font.Bold
                color: root.textColor
                Layout.fillWidth: true
            }

            // Divider
            Rectangle {
                Layout.fillWidth: true
                height: 1
                color: root.borderColor
            }

            // Conversation title
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Conversation Title"
                    font.pixelSize: 14
                    color: root.textColor
                }

                TextField {
                    id: titleField
                    Layout.fillWidth: true
                    placeholderText: "Enter a title for your conversation"
                    text: "New Conversation"
                }
            }

            // Template selection
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Template"
                    font.pixelSize: 14
                    color: root.textColor
                }

                ComboBox {
                    id: templateComboBox
                    Layout.fillWidth: true
                    model: ["None"].concat(newConversationDialog.templates.map(function(template) { return template.templateName; }))

                    onActivated: {
                        if (currentIndex > 0) {
                            // Apply template settings
                            var template = newConversationDialog.templates[currentIndex - 1];
                            providerComboBox.currentIndex = providerComboBox.indexOfValue(template.provider);

                            // Wait for the model list to update before setting the model
                            Qt.callLater(function() {
                                modelComboBox.currentIndex = Math.max(0, modelComboBox.find(template.model));
                                systemPromptField.text = template.systemPrompt;
                                temperatureSlider.value = template.temperature;
                                maxTokensSlider.value = template.maxTokens;
                            });
                        }
                    }
                }
            }

            // Provider selection
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Provider"
                    font.pixelSize: 14
                    color: root.textColor
                }

                ComboBox {
                    id: providerComboBox
                    Layout.fillWidth: true
                    model: settingsViewModel.availableProviders
                    textRole: "displayName"
                    valueRole: "name"

                    Component.onCompleted: {
                        currentIndex = indexOfValue(settingsViewModel.selectedProvider)
                    }
                }
            }

            // Model selection
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Model"
                    font.pixelSize: 14
                    color: root.textColor
                }

                ComboBox {
                    id: modelComboBox
                    Layout.fillWidth: true

                    // Get available models based on the selected provider
                    property var modelsByProvider: {
                        "openai": ["gpt-4o", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
                        "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                        "placeholder": ["placeholder-model"]
                    }

                    model: modelsByProvider[providerComboBox.currentValue] || ["default-model"]

                    // Update model list when provider changes
                    Connections {
                        target: providerComboBox
                        function onCurrentValueChanged() {
                            var provider = providerComboBox.currentValue
                            modelComboBox.model = modelComboBox.modelsByProvider[provider] || ["default-model"]
                            modelComboBox.currentIndex = 0
                        }
                    }
                }
            }

            // System prompt
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "System Prompt"
                    font.pixelSize: 14
                    color: root.textColor
                }

                TextArea {
                    id: systemPromptField
                    Layout.fillWidth: true
                    Layout.preferredHeight: 80
                    placeholderText: "Enter a system prompt to define the assistant's behavior"
                    wrapMode: TextEdit.Wrap
                    text: "You are a helpful assistant."

                    background: Rectangle {
                        color: root.inputBackgroundColor
                        border.color: root.borderColor
                        border.width: 1
                        radius: 4
                    }
                }
            }

            // Temperature
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Temperature: " + temperatureSlider.value.toFixed(1)
                    font.pixelSize: 14
                    color: root.textColor
                }

                Slider {
                    id: temperatureSlider
                    Layout.fillWidth: true
                    from: 0
                    to: 1
                    stepSize: 0.1
                    value: 0.7
                }
            }

            // Max Tokens
            ColumnLayout {
                Layout.fillWidth: true
                spacing: 8

                Label {
                    text: "Max Tokens: " + maxTokensSlider.value
                    font.pixelSize: 14
                    color: root.textColor
                }

                Slider {
                    id: maxTokensSlider
                    Layout.fillWidth: true
                    from: 500
                    to: 4000
                    stepSize: 100
                    value: 2000
                }
            }

            // Buttons
            RowLayout {
                Layout.fillWidth: true
                Layout.topMargin: 16
                spacing: 10

                Item {
                    Layout.fillWidth: true
                }

                Button {
                    text: "Cancel"
                    onClicked: {
                        newConversationDialog.visible = false
                        newConversationDialog.closed()
                    }
                }

                Button {
                    text: "Create"
                    highlighted: true
                    onClicked: {
                        newConversationDialog.conversationCreated(
                            titleField.text,
                            providerComboBox.currentValue,
                            modelComboBox.currentText,
                            systemPromptField.text,
                            temperatureSlider.value,
                            maxTokensSlider.value
                        )
                        newConversationDialog.visible = false
                        newConversationDialog.closed()
                    }
                }
            }
        }
    }
}