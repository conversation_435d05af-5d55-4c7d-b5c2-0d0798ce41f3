import QtQuick 2.15
import QtQuick.Controls 2.15

Button {
    id: filePickerButton

    // Properties
    property var nameFilters: ["All files (*)"]
    property string title: "Select File"
    property bool multipleFiles: false

    // Signals
    signal fileSelected(string filePath, string fileName)
    signal filesSelected(var filePaths, var fileNames)

    text: "Attach"
    icon.source: "qrc:/icons/attachment.svg"

    // Simplified version for build testing
    onClicked: {
        console.log("File picker button clicked")
        // Simulate file selection
        if (multipleFiles) {
            var paths = ["/path/to/file1.txt", "/path/to/file2.txt"];
            var names = ["file1.txt", "file2.txt"];
            filePickerButton.filesSelected(paths, names);
        } else {
            var filePath = "/path/to/file.txt";
            var fileName = "file.txt";
            filePickerButton.fileSelected(filePath, fileName);
        }
    }
}