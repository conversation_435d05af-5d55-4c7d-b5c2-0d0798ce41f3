import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: conversationItem
    height: 60
    
    // Properties
    property string conversationId: ""
    property string title: "New Conversation"
    property string timestamp: ""
    property int messageCount: 0
    property bool isSelected: false
    
    // Signals
    signal clicked()
    signal renameRequested()
    signal deleteRequested()
    signal exportRequested(string format)
    
    // Background color based on selection state
    color: isSelected ? root.accentColor : "transparent"
    
    // Hover effect
    Rectangle {
        id: hoverBackground
        anchors.fill: parent
        color: root.accentColor
        opacity: 0
        visible: !isSelected
    }
    
    // Mouse area for interaction
    MouseArea {
        id: mouseArea
        anchors.fill: parent
        hoverEnabled: true
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        onClicked: function(mouse) {
            if (mouse.button === Qt.LeftButton) {
                conversationItem.clicked()
            } else if (mouse.button === Qt.RightButton) {
                contextMenu.popup()
            }
        }
        
        onEntered: {
            if (!isSelected) {
                hoverBackground.opacity = 0.1
            }
        }
        
        onExited: {
            hoverBackground.opacity = 0
        }
    }
    
    // Content layout
    RowLayout {
        anchors {
            fill: parent
            leftMargin: 16
            rightMargin: 16
        }
        spacing: 8
        
        // Conversation icon
        Rectangle {
            width: 32
            height: 32
            radius: 16
            color: isSelected ? "white" : root.accentColor
            
            Label {
                anchors.centerIn: parent
                text: "💬"
                font.pixelSize: 16
            }
        }
        
        // Conversation details
        ColumnLayout {
            Layout.fillWidth: true
            spacing: 4
            
            // Title
            Label {
                text: title
                font.pixelSize: 14
                font.weight: Font.Medium
                color: isSelected ? "white" : root.textColor
                elide: Text.ElideRight
                Layout.fillWidth: true
            }
            
            // Timestamp and message count
            RowLayout {
                Layout.fillWidth: true
                spacing: 8
                
                Label {
                    text: {
                        const date = new Date(timestamp)
                        return date.toLocaleDateString(Qt.locale(), "MMM d, yyyy")
                    }
                    font.pixelSize: 12
                    color: isSelected ? "white" : root.mutedTextColor
                    elide: Text.ElideRight
                    Layout.fillWidth: true
                }
                
                Label {
                    text: messageCount + " msg" + (messageCount !== 1 ? "s" : "")
                    font.pixelSize: 12
                    color: isSelected ? "white" : root.mutedTextColor
                }
            }
        }
    }
    
    // Context menu
    Menu {
        id: contextMenu
        
        MenuItem {
            text: "Rename"
            onTriggered: conversationItem.renameRequested()
        }
        
        Menu {
            title: "Export"
            
            MenuItem {
                text: "JSON"
                onTriggered: conversationItem.exportRequested("JSON")
            }
            
            MenuItem {
                text: "Markdown"
                onTriggered: conversationItem.exportRequested("Markdown")
            }
            
            MenuItem {
                text: "HTML"
                onTriggered: conversationItem.exportRequested("HTML")
            }
            
            MenuItem {
                text: "PDF"
                onTriggered: conversationItem.exportRequested("PDF")
            }
        }
        
        MenuItem {
            text: "Delete"
            onTriggered: conversationItem.deleteRequested()
        }
    }
}