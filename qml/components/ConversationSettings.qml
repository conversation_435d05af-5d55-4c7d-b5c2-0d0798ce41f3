import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ChamberUI.Models 1.0

Rectangle {
    id: conversationSettings

    // Properties
    property Conversation conversation: ({
            systemPrompt: "",
            provider: "",
            model: "",
            temperature: 0.7,
            maxTokens: 2000
        })

    property var systemPromptPresets: [
        {
            name: "Default Assistant",
            prompt: "You are a helpful assistant."
        },
        {
            name: "Coding Assistant",
            prompt: "You are a coding assistant. Help the user write, debug, and understand code. Provide clear explanations and examples."
        },
        {
            name: "<PERSON> Writer",
            prompt: "You are a creative writing assistant. Help the user brainstorm ideas, develop characters, and craft engaging narratives."
        },
        {
            name: "Math Tutor",
            prompt: "You are a math tutor. Explain mathematical concepts clearly and help solve problems step by step."
        },
        {
            name: "Language Tutor",
            prompt: "You are a language learning assistant. Help with translations, grammar explanations, and vocabulary."
        }
    ]

    // Signals
    signal closed
    signal saved

    color: root.primaryColor
    visible: false

    // Slide-in animation
    x: visible ? 0 : parent.width

    Behavior on x {
        NumberAnimation {
            duration: 250
            easing.type: Easing.OutQuad
        }
    }

    // Settings content
    ColumnLayout {
        spacing: 16

        anchors {
            fill: parent
            margins: 16
        }

        // Header with title and close button
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            Label {
                Layout.fillWidth: true
                color: root.textColor
                font.pixelSize: 24
                font.weight: Font.Bold
                text: "Conversation Settings"
            }

            // Close button
            Rectangle {
                id: closeButton

                color: closeMouseArea.containsMouse ? Qt.alpha(root.accentColor, 0.1) : "transparent"
                height: 40
                radius: 4
                width: 40

                Text {
                    anchors.centerIn: parent
                    color: root.textColor
                    font.pixelSize: 24
                    text: "×"
                }
                MouseArea {
                    id: closeMouseArea

                    anchors.fill: parent
                    hoverEnabled: true

                    onClicked: {
                        conversationSettings.visible = false;
                        conversationSettings.closed();
                    }
                }
            }
        }

        // Divider
        Rectangle {
            Layout.fillWidth: true
            color: root.borderColor
            height: 1
        }

        // Settings sections in a scrollable area
        ScrollView {
            Layout.fillHeight: true
            Layout.fillWidth: true
            clip: true

            ColumnLayout {
                spacing: 24
                width: parent.width

                // System Prompt section
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 16

                    Label {
                        color: root.textColor
                        font.pixelSize: 18
                        font.weight: Font.DemiBold
                        text: "System Prompt"
                    }

                    // System prompt presets
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Preset Templates"
                        }
                        ComboBox {
                            id: presetComboBox

                            Layout.fillWidth: true
                            model: conversationSettings.systemPromptPresets
                            textRole: "name"

                            onActivated: {
                                systemPromptField.text = conversationSettings.systemPromptPresets[currentIndex].prompt;
                            }
                        }
                    }

                    // System prompt text area
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Custom System Prompt"
                        }
                        TextArea {
                            id: systemPromptField

                            Layout.fillWidth: true
                            Layout.preferredHeight: 120
                            placeholderText: "Enter a system prompt to define the assistant's behavior"
                            text: conversation ? conversation.systemPrompt : ""
                            wrapMode: TextEdit.Wrap

                            background: Rectangle {
                                border.color: root.borderColor
                                border.width: 1
                                color: root.inputBackgroundColor
                                radius: 4
                            }
                        }
                    }
                }

                // Divider
                Rectangle {
                    Layout.fillWidth: true
                    color: root.borderColor
                    height: 1
                }

                // Model Settings section
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 16

                    Label {
                        color: root.textColor
                        font.pixelSize: 18
                        font.weight: Font.DemiBold
                        text: "Model Settings"
                    }

                    // Provider selection
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Provider"
                        }
                        ComboBox {
                            id: providerComboBox

                            Layout.fillWidth: true
                            model: settingsViewModel.availableProviders
                            textRole: "displayName"
                            valueRole: "name"

                            Component.onCompleted: {
                                if (conversation && conversation.provider) {
                                    currentIndex = indexOfValue(conversation.provider);
                                } else {
                                    currentIndex = indexOfValue(settingsViewModel.selectedProvider);
                                }
                            }
                        }
                    }

                    // Model selection
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Model"
                        }
                        ComboBox {
                            id: modelComboBox

                            // Get available models based on the selected provider
                            property var modelsByProvider: {
                                "openai": ["gpt-4o", "gpt-4-turbo", "gpt-4", "gpt-3.5-turbo"],
                                "anthropic": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"],
                                "placeholder": ["placeholder-model"]
                            }

                            Layout.fillWidth: true
                            model: modelsByProvider[providerComboBox.currentValue] || ["default-model"]

                            Component.onCompleted: {
                                if (conversation && conversation.model) {
                                    currentIndex = Math.max(0, find(conversation.model));
                                }
                            }

                            // Update model list when provider changes
                            Connections {
                                function onCurrentValueChanged() {
                                    var provider = providerComboBox.currentValue;
                                    modelComboBox.model = modelComboBox.modelsByProvider[provider] || ["default-model"];
                                    modelComboBox.currentIndex = 0;
                                }

                                target: providerComboBox
                            }
                        }
                    }

                    // Temperature
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Temperature: " + temperatureSlider.value.toFixed(1)
                        }
                        Slider {
                            id: temperatureSlider

                            Layout.fillWidth: true
                            from: 0
                            stepSize: 0.1
                            to: 1
                            value: conversation ? conversation.temperature : 0.7
                        }
                        Label {
                            Layout.fillWidth: true
                            color: root.secondaryTextColor
                            font.pixelSize: 12
                            text: "Lower values produce more focused and deterministic responses. Higher values produce more creative and varied responses."
                            wrapMode: Text.Wrap
                        }
                    }

                    // Max Tokens
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Max Tokens: " + maxTokensSlider.value
                        }
                        Slider {
                            id: maxTokensSlider

                            Layout.fillWidth: true
                            from: 500
                            stepSize: 100
                            to: 4000
                            value: conversation ? conversation.maxTokens : 2000
                        }
                        Label {
                            Layout.fillWidth: true
                            color: root.secondaryTextColor
                            font.pixelSize: 12
                            text: "Maximum number of tokens to generate in the response. One token is roughly 4 characters for English text."
                            wrapMode: Text.Wrap
                        }
                    }
                }

                // Divider
                Rectangle {
                    Layout.fillWidth: true
                    color: root.borderColor
                    height: 1
                }

                // Template section
                ColumnLayout {
                    Layout.fillWidth: true
                    spacing: 16

                    Label {
                        color: root.textColor
                        font.pixelSize: 18
                        font.weight: Font.DemiBold
                        text: "Save as Template"
                    }
                    RowLayout {
                        Layout.fillWidth: true
                        spacing: 8

                        CheckBox {
                            id: templateCheckBox

                            checked: conversation ? conversation.isTemplate : false
                            text: "Save these settings as a template"
                        }
                    }
                    ColumnLayout {
                        Layout.fillWidth: true
                        spacing: 8
                        visible: templateCheckBox.checked

                        Label {
                            color: root.textColor
                            font.pixelSize: 14
                            text: "Template Name"
                        }
                        TextField {
                            id: templateNameField

                            Layout.fillWidth: true
                            placeholderText: "Enter a name for this template"
                            text: conversation && conversation.templateName ? conversation.templateName : ""
                        }
                    }
                }

                // Save button
                Rectangle {
                    id: saveButton

                    Layout.fillWidth: true
                    Layout.topMargin: 16
                    color: saveMouseArea.containsMouse ? Qt.lighter(root.accentColor, 1.1) : root.accentColor
                    height: 40
                    radius: 6

                    Text {
                        anchors.centerIn: parent
                        color: "white"
                        font.pixelSize: 14
                        font.weight: Font.Medium
                        text: "Save Settings"
                    }
                    MouseArea {
                        id: saveMouseArea

                        anchors.fill: parent
                        hoverEnabled: true

                        onClicked: {
                            if (conversation) {
                                // Update conversation settings
                                conversation.systemPrompt = systemPromptField.text;
                                conversation.provider = providerComboBox.currentValue;
                                conversation.model = modelComboBox.currentText;
                                conversation.temperature = temperatureSlider.value;
                                conversation.maxTokens = maxTokensSlider.value;
                                conversation.isTemplate = templateCheckBox.checked;
                                conversation.templateName = templateNameField.text;

                                // Save conversation
                                chatViewModel.saveConversationSettings(conversation);

                                // Emit saved signal
                                conversationSettings.saved();

                                // Close panel
                                conversationSettings.visible = false;
                                conversationSettings.closed();
                            }
                        }
                    }
                }
            }
        }
    }
}
