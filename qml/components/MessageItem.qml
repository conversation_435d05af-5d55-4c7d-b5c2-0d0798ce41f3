import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1
import QtCore
import "."

Item {
    id: messageItem
    width: parent.width
    height: messageLayout.height

    // Properties
    property string messageId: ""
    property string content: ""
    property string role: "user"
    property string timestamp: ""
    property var attachments: [] // Array of attachment objects
    property bool isSpeaking: false

    // Computed properties
    property bool isUser: role === "user"
    property bool isAssistant: role === "assistant"
    property bool isSystem: role === "system"
    property bool hasAttachments: attachments && attachments.length > 0

    // Text-to-speech component
    TextToSpeech {
        id: textToSpeech
        anchors.centerIn: parent
        isVisible: false
        z: 10
        text: content
    }

    // Message layout
    ColumnLayout {
        id: messageLayout
        width: parent.width
        spacing: 4

        // Role indicator and timestamp
        RowLayout {
            Layout.fillWidth: true
            spacing: 8

            // Role indicator
            Label {
                text: {
                    if (isUser) return "You"
                    if (isAssistant) return "AI"
                    return "System"
                }
                font.pixelSize: 12
                font.weight: Font.Medium
                color: {
                    if (isUser) return root.accentColor
                    if (isAssistant) return root.mutedTextColor
                    return "#6c757d"
                }
            }

            // Timestamp
            Label {
                text: {
                    const date = new Date(timestamp)
                    return date.toLocaleTimeString(Qt.locale(), "hh:mm a")
                }
                font.pixelSize: 12
                color: root.mutedTextColor
                Layout.fillWidth: true
            }

            // Speak button (only for assistant messages)
            Button {
                id: speakButton
                visible: isAssistant
                implicitWidth: 24
                implicitHeight: 24

                // Icon
                contentItem: Text {
                    text: messageItem.isSpeaking ? "🔊" : "🔈"
                    font.pixelSize: 14
                    color: root.mutedTextColor
                    horizontalAlignment: Text.AlignHCenter
                    verticalAlignment: Text.AlignVCenter
                }

                onClicked: {
                    if (messageItem.isSpeaking) {
                        voiceService.stopSpeech()
                        messageItem.isSpeaking = false
                        textToSpeech.isVisible = false
                    } else {
                        textToSpeech.isVisible = true
                        messageItem.isSpeaking = true
                        voiceService.speak(content)
                    }
                }
            }

            // Attachment indicator
            Label {
                text: hasAttachments ? attachments.length + " attachment" + (attachments.length > 1 ? "s" : "") : ""
                font.pixelSize: 12
                color: root.mutedTextColor
                visible: hasAttachments
            }
        }

        // Message bubble
        Rectangle {
            id: messageBubble
            Layout.fillWidth: true
            Layout.preferredWidth: Math.min(messageContent.implicitWidth + 32, messageItem.width * 0.8)
            Layout.alignment: isUser ? Qt.AlignRight : Qt.AlignLeft
            height: messageContent.implicitHeight + 24
            radius: 8

            color: {
                if (isUser) return root.accentColor
                if (isAssistant) return root.secondaryColor
                return "#6c757d"
            }

            // Message content
            MarkdownRenderer {
                id: messageContent
                anchors {
                    fill: parent
                    margins: 12
                }
                text: content
                textColor: {
                    if (isUser) return "white"
                    return root.textColor
                }
                linkColor: {
                    if (isUser) return "#a2d8ff"
                    return "#0366d6"
                }
                codeBackgroundColor: {
                    if (isUser) return Qt.rgba(1, 1, 1, 0.1)
                    return "#f6f8fa"
                }
                blockquoteColor: {
                    if (isUser) return Qt.rgba(1, 1, 1, 0.3)
                    return "#e0e0e0"
                }
                fontSize: 14

                // Handle link activation
                onLinkActivated: function(link) {
                    Qt.openUrlExternally(link);
                }
            }

            // Context menu for message actions
            MouseArea {
                anchors.fill: parent
                acceptedButtons: Qt.RightButton

                onClicked: {
                    if (mouse.button === Qt.RightButton) {
                        contextMenu.popup()
                    }
                }
            }

            Menu {
                id: contextMenu

                MenuItem {
                    text: "Copy"
                    onTriggered: {
                        // Use Qt.application.clipboard in Qt 6
                        Qt.application.clipboard.setText(content);
                    }
                }

                MenuItem {
                    text: "Copy as Markdown"
                    onTriggered: {
                        // Use Qt.application.clipboard in Qt 6
                        Qt.application.clipboard.setText(content);
                    }
                }

                MenuItem {
                    text: "Delete"
                    onTriggered: {
                        // TODO: Implement delete message
                        chatViewModel.deleteMessage(messageId);
                    }
                }
            }
        }

        // Attachments
        Column {
            id: attachmentsColumn
            Layout.fillWidth: true
            Layout.alignment: isUser ? Qt.AlignRight : Qt.AlignLeft
            Layout.preferredWidth: Math.min(messageItem.width * 0.8, 600)
            spacing: 8
            visible: hasAttachments

            Repeater {
                model: attachments

                delegate: FileAttachment {
                    width: attachmentsColumn.width
                    filename: modelData.filename
                    filePath: modelData.path
                    fileType: modelData.type
                    mimeType: modelData.mimeType
                    fileSize: modelData.size || 0
                }
            }
        }

        // No need for Clipboard object in Qt 6, we'll use QGuiApplication.clipboard

        // Handle speech events
        Connections {
            target: voiceService

            function onSpeechFinished() {
                messageItem.isSpeaking = false
            }

            function onSpeechStopped() {
                messageItem.isSpeaking = false
            }
        }
    }
}