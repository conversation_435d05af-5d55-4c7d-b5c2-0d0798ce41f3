import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.platform 1.1

Rectangle {
    id: codeBlock
    width: parent.width
    height: codeLayout.height + 16
    radius: 4
    
    // Properties
    property string code: ""
    property string language: ""
    property color backgroundColor: "#f6f8fa"
    property color textColor: "#24292e"
    property int fontSize: 14
    property bool copyEnabled: true
    
    // Colors for syntax highlighting
    property var syntaxColors: {
        "keyword": "#d73a49",      // red
        "string": "#032f62",       // blue
        "comment": "#6a737d",      // gray
        "number": "#005cc5",       // blue
        "function": "#6f42c1",     // purple
        "operator": "#d73a49",     // red
        "variable": "#e36209",     // orange
        "type": "#6f42c1",         // purple
        "builtin": "#005cc5",      // blue
        "tag": "#22863a",          // green
        "attribute": "#6f42c1"     // purple
    }
    
    color: backgroundColor
    
    // Copy notification
    Rectangle {
        id: copyNotification
        anchors.right: parent.right
        anchors.top: parent.top
        anchors.margins: 8
        width: copyText.width + 16
        height: copyText.height + 8
        radius: 4
        color: "#2ecc71"
        opacity: 0
        visible: opacity > 0
        
        Text {
            id: copyText
            anchors.centerIn: parent
            text: "Copied!"
            color: "white"
            font.pixelSize: 12
        }
        
        // Animation for copy notification
        NumberAnimation {
            id: fadeAnimation
            target: copyNotification
            property: "opacity"
            from: 1
            to: 0
            duration: 2000
            easing.type: Easing.InOutQuad
        }
    }
    
    ColumnLayout {
        id: codeLayout
        width: parent.width - 16
        anchors.top: parent.top
        anchors.left: parent.left
        anchors.margins: 8
        spacing: 8
        
        // Language indicator and copy button
        RowLayout {
            Layout.fillWidth: true
            spacing: 8
            
            // Language indicator
            Label {
                text: language || "code"
                font.pixelSize: 12
                font.family: "Menlo, Monaco, 'Courier New', monospace"
                color: Qt.rgba(textColor.r, textColor.g, textColor.b, 0.6)
            }
            
            Item { Layout.fillWidth: true }
            
            // Copy button
            Button {
                id: copyButton
                visible: copyEnabled
                text: "Copy"
                font.pixelSize: 12
                implicitHeight: 24
                implicitWidth: 60
                
                background: Rectangle {
                    color: copyButton.hovered ? Qt.rgba(0, 0, 0, 0.1) : Qt.rgba(0, 0, 0, 0.05)
                    radius: 4
                }
                
                onClicked: {
                    clipboard.text = code;
                    fadeAnimation.start();
                }
            }
        }
        
        // Code content
        Flickable {
            id: codeFlickable
            Layout.fillWidth: true
            Layout.preferredHeight: Math.min(codeText.implicitHeight, 300)
            contentWidth: codeText.implicitWidth
            contentHeight: codeText.implicitHeight
            clip: true
            
            ScrollBar.vertical: ScrollBar {
                active: true
                policy: codeText.implicitHeight > 300 ? ScrollBar.AlwaysOn : ScrollBar.AsNeeded
            }
            
            ScrollBar.horizontal: ScrollBar {
                active: true
                policy: codeText.implicitWidth > codeFlickable.width ? ScrollBar.AlwaysOn : ScrollBar.AsNeeded
            }
            
            TextEdit {
                id: codeText
                width: codeFlickable.width
                text: highlightSyntax(code, language)
                font.family: "Menlo, Monaco, 'Courier New', monospace"
                font.pixelSize: fontSize
                color: textColor
                textFormat: TextEdit.RichText
                readOnly: true
                selectByMouse: true
                wrapMode: TextEdit.NoWrap
                leftPadding: 8
                rightPadding: 8
                topPadding: 4
                bottomPadding: 4
            }
        }
    }
    
    // Clipboard for copy functionality
    Clipboard {
        id: clipboard
    }
    
    // Function to highlight syntax based on language
    function highlightSyntax(code, language) {
        if (!code) return "";
        
        // Escape HTML special characters
        let highlightedCode = code.replace(/&/g, "&amp;")
                                  .replace(/</g, "&lt;")
                                  .replace(/>/g, "&gt;");
        
        // Apply language-specific highlighting
        switch (language.toLowerCase()) {
            case "js":
            case "javascript":
                return highlightJavaScript(highlightedCode);
            case "py":
            case "python":
                return highlightPython(highlightedCode);
            case "cpp":
            case "c++":
                return highlightCpp(highlightedCode);
            case "json":
                return highlightJson(highlightedCode);
            case "html":
                return highlightHtml(highlightedCode);
            case "css":
                return highlightCss(highlightedCode);
            case "bash":
            case "sh":
                return highlightBash(highlightedCode);
            case "xml":
                return highlightXml(highlightedCode);
            case "markdown":
            case "md":
                return highlightMarkdown(highlightedCode);
            case "sql":
                return highlightSql(highlightedCode);
            default:
                return highlightedCode;
        }
    }
    
    // JavaScript syntax highlighting
    function highlightJavaScript(code) {
        // Keywords
        const keywords = ["var", "let", "const", "function", "return", "if", "else", "for", "while", "do", 
                         "switch", "case", "break", "continue", "new", "try", "catch", "throw", "finally",
                         "class", "extends", "super", "import", "export", "from", "as", "async", "await",
                         "yield", "typeof", "instanceof", "in", "of", "delete", "void", "this"];
        
        // Apply keyword highlighting
        let result = code;
        
        // Highlight strings
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight comments
        result = result.replace(/\/\/.*$/gm, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        result = result.replace(/\/\*[\s\S]*?\*\//g, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight numbers
        result = result.replace(/\b(\d+(\.\d+)?)\b/g, "<span style='color:" + syntaxColors.number + "'>$&</span>");
        
        // Highlight keywords
        for (const keyword of keywords) {
            const regex = new RegExp("\\b" + keyword + "\\b", "g");
            result = result.replace(regex, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        }
        
        // Highlight function declarations
        result = result.replace(/\b([a-zA-Z_$][a-zA-Z0-9_$]*)\s*\(/g, 
                              "<span style='color:" + syntaxColors.function + "'>$1</span>(");
        
        return result;
    }
    
    // Python syntax highlighting
    function highlightPython(code) {
        // Keywords
        const keywords = ["and", "as", "assert", "break", "class", "continue", "def", "del", "elif", "else",
                         "except", "False", "finally", "for", "from", "global", "if", "import", "in", "is",
                         "lambda", "None", "nonlocal", "not", "or", "pass", "raise", "return", "True", "try",
                         "while", "with", "yield"];
        
        // Apply keyword highlighting
        let result = code;
        
        // Highlight strings
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        result = result.replace(/(["'])([^"']*?)(\\["'])([^"']*?)\1/g, 
                              "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight triple-quoted strings
        result = result.replace(/"""[\s\S]*?"""/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        result = result.replace(/'''[\s\S]*?'''/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight comments
        result = result.replace(/#.*$/gm, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight numbers
        result = result.replace(/\b(\d+(\.\d+)?)\b/g, "<span style='color:" + syntaxColors.number + "'>$&</span>");
        
        // Highlight keywords
        for (const keyword of keywords) {
            const regex = new RegExp("\\b" + keyword + "\\b", "g");
            result = result.replace(regex, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        }
        
        // Highlight function declarations
        result = result.replace(/\bdef\s+([a-zA-Z_][a-zA-Z0-9_]*)/g, 
                              "def <span style='color:" + syntaxColors.function + "'>$1</span>");
        
        return result;
    }
    
    // C++ syntax highlighting
    function highlightCpp(code) {
        // Keywords
        const keywords = ["auto", "break", "case", "catch", "class", "const", "continue", "default", "delete",
                         "do", "else", "enum", "explicit", "export", "extern", "for", "friend", "goto", "if",
                         "inline", "mutable", "namespace", "new", "operator", "private", "protected", "public",
                         "register", "return", "sizeof", "static", "struct", "switch", "template", "this",
                         "throw", "try", "typedef", "typeid", "typename", "union", "using", "virtual", "void",
                         "volatile", "while", "bool", "int", "float", "double", "char", "unsigned", "signed",
                         "short", "long"];
        
        // Apply keyword highlighting
        let result = code;
        
        // Highlight strings
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight comments
        result = result.replace(/\/\/.*$/gm, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        result = result.replace(/\/\*[\s\S]*?\*\//g, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight numbers
        result = result.replace(/\b(\d+(\.\d+)?)\b/g, "<span style='color:" + syntaxColors.number + "'>$&</span>");
        
        // Highlight preprocessor directives
        result = result.replace(/^\s*#\s*\w+/gm, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        
        // Highlight keywords
        for (const keyword of keywords) {
            const regex = new RegExp("\\b" + keyword + "\\b", "g");
            result = result.replace(regex, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        }
        
        // Highlight function declarations
        result = result.replace(/\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/g, 
                              "<span style='color:" + syntaxColors.function + "'>$1</span>(");
        
        return result;
    }
    
    // JSON syntax highlighting
    function highlightJson(code) {
        let result = code;
        
        // Highlight strings (including keys)
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight numbers
        result = result.replace(/\b(\d+(\.\d+)?)\b/g, "<span style='color:" + syntaxColors.number + "'>$&</span>");
        
        // Highlight booleans and null
        result = result.replace(/\b(true|false|null)\b/g, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        
        return result;
    }
    
    // HTML syntax highlighting
    function highlightHtml(code) {
        let result = code;
        
        // Highlight tags
        result = result.replace(/(&lt;[\/!]?)([a-zA-Z0-9_-]+)/g, 
                              "$1<span style='color:" + syntaxColors.tag + "'>$2</span>");
        result = result.replace(/(&lt;[\/!]?[a-zA-Z0-9_-]+)([^&]*)(&gt;)/g, function(match, p1, p2, p3) {
            // Highlight attributes
            const withHighlightedAttrs = p2.replace(/([a-zA-Z0-9_-]+)=/g, 
                                                  "<span style='color:" + syntaxColors.attribute + "'>$1</span>=");
            return p1 + withHighlightedAttrs + p3;
        });
        
        // Highlight strings within tags
        result = result.replace(/=(&quot;|')(.*?)(&quot;|')/g, 
                              "=<span style='color:" + syntaxColors.string + "'>$1$2$3</span>");
        
        // Highlight comments
        result = result.replace(/&lt;!--[\s\S]*?--&gt;/g, 
                              "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        return result;
    }
    
    // CSS syntax highlighting
    function highlightCss(code) {
        let result = code;
        
        // Highlight selectors
        result = result.replace(/([a-zA-Z0-9_\-\.#:]+)(\s*\{)/g, 
                              "<span style='color:" + syntaxColors.tag + "'>$1</span>$2");
        
        // Highlight properties
        result = result.replace(/(\{|\;)(\s*)([a-zA-Z\-]+)(\s*:)/g, 
                              "$1$2<span style='color:" + syntaxColors.attribute + "'>$3</span>$4");
        
        // Highlight values
        result = result.replace(/(:)(\s*)([^;{}]+)(\s*)([\;}])/g, function(match, p1, p2, p3, p4, p5) {
            // Highlight numbers and units
            const valueWithHighlightedNumbers = p3.replace(/(\d+)(px|em|rem|%|vh|vw|s|ms|deg|rad)?/g, 
                                                         "<span style='color:" + syntaxColors.number + "'>$1$2</span>");
            return p1 + p2 + valueWithHighlightedNumbers + p4 + p5;
        });
        
        // Highlight comments
        result = result.replace(/\/\*[\s\S]*?\*\//g, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        return result;
    }
    
    // Bash syntax highlighting
    function highlightBash(code) {
        // Keywords
        const keywords = ["if", "then", "else", "elif", "fi", "case", "esac", "for", "while", "until", "do", 
                         "done", "in", "function", "time", "select", "break", "continue", "return", "exit"];
        
        // Apply keyword highlighting
        let result = code;
        
        // Highlight strings
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight comments
        result = result.replace(/#.*$/gm, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight variables
        result = result.replace(/\$([a-zA-Z0-9_]+|\{[a-zA-Z0-9_]+\})/g, 
                              "<span style='color:" + syntaxColors.variable + "'>$&</span>");
        
        // Highlight keywords
        for (const keyword of keywords) {
            const regex = new RegExp("\\b" + keyword + "\\b", "g");
            result = result.replace(regex, "<span style='color:" + syntaxColors.keyword + "'>$&</span>");
        }
        
        return result;
    }
    
    // XML syntax highlighting
    function highlightXml(code) {
        let result = code;
        
        // Highlight tags
        result = result.replace(/(&lt;[\/!]?)([a-zA-Z0-9_:-]+)/g, 
                              "$1<span style='color:" + syntaxColors.tag + "'>$2</span>");
        result = result.replace(/(&lt;[\/!]?[a-zA-Z0-9_:-]+)([^&]*)(&gt;)/g, function(match, p1, p2, p3) {
            // Highlight attributes
            const withHighlightedAttrs = p2.replace(/([a-zA-Z0-9_:-]+)=/g, 
                                                  "<span style='color:" + syntaxColors.attribute + "'>$1</span>=");
            return p1 + withHighlightedAttrs + p3;
        });
        
        // Highlight strings within tags
        result = result.replace(/=(&quot;|')(.*?)(&quot;|')/g, 
                              "=<span style='color:" + syntaxColors.string + "'>$1$2$3</span>");
        
        // Highlight comments
        result = result.replace(/&lt;!--[\s\S]*?--&gt;/g, 
                              "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight CDATA
        result = result.replace(/&lt;!\[CDATA\[[\s\S]*?\]\]&gt;/g, 
                              "<span style='color:" + syntaxColors.builtin + "'>$&</span>");
        
        return result;
    }
    
    // Markdown syntax highlighting
    function highlightMarkdown(code) {
        let result = code;
        
        // Highlight headers
        result = result.replace(/^(#{1,6})\s+(.+)$/gm, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span> <span style='color:" + syntaxColors.tag + "'>$2</span>");
        
        // Highlight emphasis
        result = result.replace(/(\*\*|__)(.*?)\1/g, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span><span style='color:" + syntaxColors.tag + "'>$2</span><span style='color:" + syntaxColors.keyword + "'>$1</span>");
        result = result.replace(/(\*|_)(.*?)\1/g, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span><span style='color:" + syntaxColors.attribute + "'>$2</span><span style='color:" + syntaxColors.keyword + "'>$1</span>");
        
        // Highlight links
        result = result.replace(/(\[)(.*?)(\])(\()(.*?)(\))/g, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span><span style='color:" + syntaxColors.string + "'>$2</span><span style='color:" + syntaxColors.keyword + "'>$3$4</span><span style='color:" + syntaxColors.attribute + "'>$5</span><span style='color:" + syntaxColors.keyword + "'>$6</span>");
        
        // Highlight code blocks
        result = result.replace(/(`{1,3})(.*?)\1/g, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span><span style='color:" + syntaxColors.builtin + "'>$2</span><span style='color:" + syntaxColors.keyword + "'>$1</span>");
        
        // Highlight lists
        result = result.replace(/^(\s*)([*+-]|\d+\.)\s/gm, 
                              "$1<span style='color:" + syntaxColors.keyword + "'>$2</span> ");
        
        // Highlight blockquotes
        result = result.replace(/^(\s*&gt;)\s+(.+)$/gm, 
                              "<span style='color:" + syntaxColors.keyword + "'>$1</span> <span style='color:" + syntaxColors.comment + "'>$2</span>");
        
        return result;
    }
    
    // SQL syntax highlighting
    function highlightSql(code) {
        // Keywords
        const keywords = ["SELECT", "FROM", "WHERE", "INSERT", "UPDATE", "DELETE", "CREATE", "ALTER", "DROP",
                         "TABLE", "INDEX", "VIEW", "TRIGGER", "PROCEDURE", "FUNCTION", "DATABASE", "SCHEMA",
                         "JOIN", "INNER", "OUTER", "LEFT", "RIGHT", "FULL", "ON", "GROUP", "BY", "HAVING",
                         "ORDER", "ASC", "DESC", "LIMIT", "OFFSET", "UNION", "ALL", "DISTINCT", "AS", "INTO",
                         "VALUES", "SET", "NULL", "NOT", "AND", "OR", "LIKE", "IN", "BETWEEN", "IS", "EXISTS",
                         "CASE", "WHEN", "THEN", "ELSE", "END", "BEGIN", "COMMIT", "ROLLBACK", "TRANSACTION"];
        
        // Apply keyword highlighting
        let result = code;
        
        // Highlight strings
        result = result.replace(/(["'])(.*?)\1/g, "<span style='color:" + syntaxColors.string + "'>$&</span>");
        
        // Highlight comments
        result = result.replace(/--.*$/gm, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        result = result.replace(/\/\*[\s\S]*?\*\//g, "<span style='color:" + syntaxColors.comment + "'>$&</span>");
        
        // Highlight numbers
        result = result.replace(/\b(\d+(\.\d+)?)\b/g, "<span style='color:" + syntaxColors.number + "'>$&</span>");
        
        // Highlight keywords (case insensitive)
        for (const keyword of keywords) {
            const regex = new RegExp("\\b" + keyword + "\\b", "gi");
            result = result.replace(regex, function(match) {
                return "<span style='color:" + syntaxColors.keyword + "'>" + match + "</span>";
            });
        }
        
        return result;
    }
}