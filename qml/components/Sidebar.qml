import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Rectangle {
    id: sidebar
    color: root.secondaryColor
    
    // Signals
    signal settingsRequested()
    signal newConversationRequested()
    signal helpRequested()
    
    ColumnLayout {
        anchors.fill: parent
        spacing: 0
        
        // Header with app title and new chat button
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 60
            color: "transparent"
            
            RowLayout {
                anchors {
                    fill: parent
                    leftMargin: 16
                    rightMargin: 16
                }
                spacing: 8
                
                // App title
                Label {
                    text: "ChamberUI"
                    font.pixelSize: 18
                    font.weight: Font.DemiBold
                    color: root.textColor
                    Layout.fillWidth: true
                }
                
                // New chat button
                Button {
                    id: newChatButton
                    text: "+"
                    font.pixelSize: 18
                    ToolTip.text: "New Conversation"
                    ToolTip.visible: hovered
                    ToolTip.delay: 500
                    
                    onClicked: {
                        newConversationRequested()
                    }
                }
                
                // Import button
                Button {
                    id: importButton
                    text: "📥"
                    font.pixelSize: 16
                    ToolTip.text: "Import Conversation"
                    ToolTip.visible: hovered
                    ToolTip.delay: 500
                    
                    onClicked: {
                        conversationListViewModel.importConversation()
                    }
                }
            }
            
            // Bottom border
            Rectangle {
                width: parent.width
                height: 1
                color: root.borderColor
                anchors.bottom: parent.bottom
            }
        }
        
        // Conversation list
        ListView {
            id: conversationList
            Layout.fillWidth: true
            Layout.fillHeight: true
            clip: true
            
            model: conversationListViewModel.conversations
            
            delegate: ConversationItem {
                width: conversationList.width
                conversationId: modelData.id
                title: modelData.title
                timestamp: modelData.timestamp
                messageCount: modelData.messageCount
                isSelected: conversationListViewModel.selectedConversationId === modelData.id
                
                onClicked: {
                    conversationListViewModel.selectConversation(modelData.id)
                }
                
                onRenameRequested: {
                    // TODO: Implement rename dialog
                    conversationListViewModel.renameConversation(modelData.id, "Renamed Conversation")
                }
                
                onDeleteRequested: {
                    conversationListViewModel.deleteConversation(modelData.id)
                }
                
                onExportRequested: function(format) {
                    conversationListViewModel.exportConversation(modelData.id, format)
                }
            }
            
            // Empty state
            Label {
                anchors.centerIn: parent
                text: "No conversations yet"
                color: root.mutedTextColor
                visible: conversationList.count === 0
            }
        }
        
        // Footer with settings button
        Rectangle {
            Layout.fillWidth: true
            Layout.preferredHeight: 50
            color: "transparent"
            
            RowLayout {
                anchors {
                    fill: parent
                    leftMargin: 16
                    rightMargin: 16
                }
                
                // App version
                Label {
                    text: "v" + appViewModel.appVersion
                    font.pixelSize: 12
                    color: root.mutedTextColor
                    Layout.fillWidth: true
                }
                
                // Settings button
                Button {
                    id: settingsButton
                    text: "⚙️"
                    font.pixelSize: 16
                    ToolTip.text: "Settings"
                    ToolTip.visible: hovered
                    ToolTip.delay: 500
                    
                    onClicked: {
                        sidebar.settingsRequested()
                    }
                }
                
                // Help button
                Button {
                    id: helpButton
                    text: "❓"
                    font.pixelSize: 16
                    ToolTip.text: "Help"
                    ToolTip.visible: hovered
                    ToolTip.delay: 500
                    
                    onClicked: {
                        sidebar.helpRequested()
                    }
                }
                
                // Dark mode toggle
                Button {
                    id: darkModeButton
                    text: appViewModel.darkMode ? "☀️" : "🌙"
                    font.pixelSize: 16
                    ToolTip.text: appViewModel.darkMode ? "Light Mode" : "Dark Mode"
                    ToolTip.visible: hovered
                    ToolTip.delay: 500
                    
                    onClicked: {
                        appViewModel.darkMode = !appViewModel.darkMode
                    }
                }
            }
            
            // Top border
            Rectangle {
                width: parent.width
                height: 1
                color: root.borderColor
                anchors.top: parent.top
            }
        }
    }
}