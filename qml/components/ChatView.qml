import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import ChamberUI.ViewModels 1.0

Rectangle {
    id: chatView
    required property ChatViewModel chatViewModel
    color: root.primaryColor

    // Signals
    signal conversationSettingsRequested()

    // Header with conversation title and settings button
    Rectangle {
        id: chatHeader
        width: parent.width
        height: 50
        color: root.secondaryColor
        visible: chatViewModel.messages.length > 0

        RowLayout {
            anchors.fill: parent
            anchors.leftMargin: 16
            anchors.rightMargin: 16
            spacing: 8

            Label {
                text: (chatViewModel.currentConversation || {})["title"] || "New Conversation"
                font.pixelSize: 16
                font.weight: Font.Medium
                color: root.textColor
                Layout.fillWidth: true
                elide: Text.ElideRight
            }

            // Settings button
            Rectangle {
                width: 36
                height: 36
                radius: 4
                color: settingsMouseArea.containsMouse ? Qt.alpha(root.accentColor, 0.1) : "transparent"

                Text {
                    anchors.centerIn: parent
                    text: "⚙️"
                    font.pixelSize: 18
                }

                MouseArea {
                    id: settingsMouseArea
                    anchors.fill: parent
                    hoverEnabled: true
                    onClicked: {
                        conversationSettingsRequested()
                    }
                }
            }
        }

        // Bottom border
        Rectangle {
            width: parent.width
            height: 1
            color: root.borderColor
            anchors.bottom: parent.bottom
        }
    }

    // Empty state when no conversation is selected
    Item {
        id: emptyState
        anchors.centerIn: parent
        width: parent.width * 0.6
        visible: chatViewModel.messages.length === 0

        ColumnLayout {
            anchors.centerIn: parent
            spacing: 20
            width: parent.width

            Label {
                text: "ChamberUI"
                font.pixelSize: 32
                font.weight: Font.Bold
                color: root.textColor
                horizontalAlignment: Text.AlignHCenter
                Layout.fillWidth: true
            }

            Label {
                text: "Your personal AI assistant"
                font.pixelSize: 18
                color: root.mutedTextColor
                horizontalAlignment: Text.AlignHCenter
                Layout.fillWidth: true
            }

            Rectangle {
                Layout.fillWidth: true
                Layout.preferredHeight: 1
                color: root.borderColor
                Layout.topMargin: 20
                Layout.bottomMargin: 20
            }

            Label {
                text: "Start a new conversation by typing a message below"
                font.pixelSize: 16
                color: root.mutedTextColor
                horizontalAlignment: Text.AlignHCenter
                Layout.fillWidth: true
            }
        }
    }

    // Message list
    ListView {
        id: messageList
        anchors.top: chatHeader.visible ? chatHeader.bottom : parent.top
        anchors.left: parent.left
        anchors.right: parent.right
        anchors.bottom: parent.bottom
        anchors.margins: 16
        spacing: 16
        clip: true

        model: chatViewModel.messages
        visible: chatViewModel.messages.length > 0

        // Auto-scroll to bottom when new messages arrive
        onCountChanged: {
            if (atYEnd || contentHeight < height) {
                positionViewAtEnd()
            }
        }

        // Add spacing at the bottom for better UX
        footer: Item {
            width: parent.width
            height: 60 // Space for the input area
        }

        // Delegate for message items
        delegate: MessageItem {
            width: messageList.width
            messageId: modelData.id
            content: modelData.content
            role: modelData.role
            timestamp: modelData.timestamp
        }

        // Scroll bar
        ScrollBar.vertical: ScrollBar {
            id: verticalScrollBar
            active: true
            interactive: true
            policy: ScrollBar.AsNeeded

            // Style the scrollbar
            contentItem: Rectangle {
                implicitWidth: 8
                radius: width / 2
                color: verticalScrollBar.pressed ? Qt.rgba(0, 0, 0, 0.3) :
                    verticalScrollBar.hovered ? Qt.rgba(0, 0, 0, 0.2) :
                        Qt.rgba(0, 0, 0, 0.1)
            }
        }
    }

    // Loading indicator
    BusyIndicator {
        id: loadingIndicator
        anchors {
            bottom: parent.bottom
            horizontalCenter: parent.horizontalCenter
            bottomMargin: 20
        }
        running: chatViewModel.isProcessing
        visible: chatViewModel.isProcessing
    }
}
