import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15

Dialog {
    id: progressDialog

    property string dialogTitle: "Operation in Progress"
    property string message: "Please wait..."
    property int progress: 0
    property bool indeterminate: false

    width: 400
    height: 150
    modal: true
    closePolicy: Dialog.NoAutoClose

    x: (parent.width - width) / 2
    y: (parent.height - height) / 2

    header: Rectangle {
        color: root.accentColor
        height: 40

        Label {
            anchors {
                left: parent.left
                leftMargin: 16
                verticalCenter: parent.verticalCenter
            }
            text: progressDialog.dialogTitle
            font.pixelSize: 16
            font.weight: Font.Medium
            color: "white"
        }
    }

    contentItem: ColumnLayout {
        spacing: 16

        Label {
            text: progressDialog.message
            Layout.fillWidth: true
            wrapMode: Text.WordWrap
            Layout.leftMargin: 16
            Layout.rightMargin: 16
            Layout.topMargin: 8
        }

        ProgressBar {
            id: progressBar
            Layout.fillWidth: true
            Layout.leftMargin: 16
            Layout.rightMargin: 16
            value: progressDialog.indeterminate ? 0 : progressDialog.progress / 100
            indeterminate: progressDialog.indeterminate
        }
    }

    footer: DialogButtonBox {
        Button {
            text: "Cancel"
            DialogButtonBox.buttonRole: DialogButtonBox.RejectRole
            visible: progressDialog.indeterminate || progressDialog.progress < 100
        }

        Button {
            text: "Close"
            DialogButtonBox.buttonRole: DialogButtonBox.AcceptRole
            visible: !progressDialog.indeterminate && progressDialog.progress >= 100
        }
    }

    onRejected: {
        // Handle cancel operation
        close()
    }

    onAccepted: {
        // Handle close
        close()
    }
}