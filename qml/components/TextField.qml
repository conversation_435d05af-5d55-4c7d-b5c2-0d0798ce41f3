import QtQuick 2.15
import QtQuick.Controls 2.15

TextField {
    id: control
    
    // Custom properties
    property color backgroundColor: root.secondaryColor
    property color textColor: root.textColor
    property color placeholderColor: root.mutedTextColor
    property color borderColor: root.borderColor
    property color focusBorderColor: root.accentColor
    property int borderWidth: 1
    property int radius: 6
    
    // Content padding
    padding: 8
    leftPadding: 12
    rightPadding: 12
    
    // Text font
    font.pixelSize: 14
    
    // Colors
    color: control.textColor
    placeholderTextColor: control.placeholderColor
    selectionColor: Qt.alpha(root.accentColor, 0.3)
    selectedTextColor: control.textColor
    
    // Background
    background: Rectangle {
        color: control.backgroundColor
        border.color: control.activeFocus ? control.focusBorderColor : control.borderColor
        border.width: control.borderWidth
        radius: control.radius
        
        // Smooth transition for focus effect
        Behavior on border.color {
            ColorAnimation {
                duration: 150
            }
        }
    }
    
    // Selection handles
    cursorDelegate: Rectangle {
        width: 2
        color: root.accentColor
        visible: control.activeFocus
    }
    
    // Cursor shape
    MouseArea {
        anchors.fill: parent
        cursorShape: Qt.IBeamCursor
        acceptedButtons: Qt.NoButton
    }
}