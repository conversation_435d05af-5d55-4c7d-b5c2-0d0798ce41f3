import QtQuick 2.15
import QtQuick.Controls 2.15

Text {
    id: markdownText
    width: parent.width
    wrapMode: Text.WordWrap
    textFormat: Text.RichText
    
    // Properties
    property color textColor: "black"
    property color linkColor: "#0366d6"
    property int fontSize: 14
    
    // Styling
    color: textColor
    font.pixelSize: fontSize
    lineHeight: 1.4
    
    // Handle link activation
    onLinkActivated: function(link) {
        Qt.openUrlExternally(link);
    }
}