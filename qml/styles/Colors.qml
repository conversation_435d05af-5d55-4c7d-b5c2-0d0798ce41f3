pragma Singleton
import QtQuick 2.15

QtObject {
    // Dark mode colors
    readonly property color darkBackground: "#1a1a1a"
    readonly property color darkSecondary: "#2d2d2d"
    readonly property color darkBorder: "#3d3d3d"
    readonly property color darkText: "#ffffff"
    readonly property color darkTextMuted: "#a0a0a0"
    
    // Light mode colors
    readonly property color lightBackground: "#ffffff"
    readonly property color lightSecondary: "#f5f5f5"
    readonly property color lightBorder: "#e0e0e0"
    readonly property color lightText: "#000000"
    readonly property color lightTextMuted: "#6c757d"
    
    // Accent colors
    readonly property color primary: "#0d6efd"
    readonly property color secondary: "#6c757d"
    readonly property color success: "#28a745"
    readonly property color danger: "#dc3545"
    readonly property color warning: "#ffc107"
    readonly property color info: "#17a2b8"
    
    // Message colors
    readonly property color userMessageBg: "#0d6efd"
    readonly property color userMessageText: "#ffffff"
    readonly property color assistantMessageBg: "#2d2d2d"
    readonly property color assistantMessageText: "#ffffff"
    readonly property color systemMessageBg: "#6c757d"
    readonly property color systemMessageText: "#ffffff"
}