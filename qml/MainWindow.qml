import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Dialogs 6.2
import "components"

Item {
    // Add keyboard shortcuts
    Shortcut {
        sequence: StandardKey.New
        onActivated: newConversationDialog.visible = true
    }

    Shortcut {
        sequence: StandardKey.Preferences
        onActivated: settingsPanel.visible = true
    }

    Shortcut {
        sequence: "Ctrl+E"
        onActivated: conversationListViewModel.exportCurrentConversation()
    }

    Shortcut {
        sequence: "Ctrl+I"
        onActivated: conversationListViewModel.importConversation()
    }

    Shortcut {
        sequence: "Ctrl+D"
        onActivated: appViewModel.setDarkMode(!appViewModel.darkMode)
    }

    Shortcut {
        sequence: "Ctrl+?"
        onActivated: helpSection.visible = true
    }
    id: mainWindow

    // Layout with sidebar and main content
    RowLayout {
        anchors.fill: parent
        spacing: 0

        // Sidebar with conversation list
        Sidebar {
            id: sidebar
            Layout.preferredWidth: 280
            Layout.fillHeight: true

            // Set minimum width
            Layout.minimumWidth: 200

            // Add a border to the right
            Rectangle {
                width: 1
                height: parent.height
                color: root.borderColor
                anchors.right: parent.right
            }

            // Connect signals
            onSettingsRequested: {
                settingsPanel.visible = true
            }

            onHelpRequested: {
                helpSection.visible = true
            }

            onNewConversationRequested: {
                newConversationDialog.visible = true
            }
        }

        // Main chat area
        Item {
            id: mainContent
            Layout.fillWidth: true
            Layout.fillHeight: true

            // Chat view showing messages
            ChatView {
                id: chatView
                anchors {
                    top: parent.top
                    left: parent.left
                    right: parent.right
                    bottom: inputArea.top
                }
                chatViewModel: globalChatViewModel

                // Connect the conversation settings request
                onConversationSettingsRequested: {
                    conversationSettingsPanel.visible = true
                }
            }

            // Input area at the bottom
            InputArea {
                id: inputArea
                height: 120
                anchors {
                    left: parent.left
                    right: parent.right
                    bottom: parent.bottom
                }
                chatViewModel: globalChatViewModel
                // Add a border to the top
                Rectangle {
                    width: parent.width
                    height: 1
                    color: root.borderColor
                    anchors.top: parent.top
                }
            }
        }
    }
    // Simple MouseArea to handle clicks outside the settings panel
    MouseArea {
        id: outsideClickArea
        anchors.fill: parent
        visible: settingsPanel.visible
        z: 10 // Lower z-index than the settings panel

        // Create a clip path to exclude the settings panel area
        onPressed: function(mouse) {
            // Only process if settings panel is visible
            if (!settingsPanel.visible) {
                return;
            }

            // Calculate actual panel rectangle using real coordinates
            // var panelRect = Qt.rect(
            //     settingsPanel.mapToItem(outsideClickArea, 0, 0).x,
            //     settingsPanel.mapToItem(outsideClickArea, 0, 0).y,
            //     settingsPanel.width,
            //     settingsPanel.height
            // );

            var panelRect = Qt.rect(settingsPanel.x, 0, settingsPanel.width, parent.height);
            console.log("Settings Panel Detailed type:", Object.prototype.toString.call(panelRect))
            if (!panelRect.contains(Qt.point(mouse.x, mouse.y))) {
                // Click is outside the panel, close it
                settingsPanel.visible = false;
                mouse.accepted = true;
            } else {
                // Click is inside the panel area, don't handle it
                mouse.accepted = false;
            }
        }
    }

    // Settings panel (slides in from the right)
    SettingsPanel {
        id: settingsPanel
        width: Math.min(500, parent.width * 0.8)
        height: parent.height
        anchors.right: parent.right
        visible: false
        z: 1000 // Ensure it's well above other elements

        // Make sure the panel is above the MouseArea
        Component.onCompleted: {
            // Ensure the panel is properly initialized
            console.log("Settings panel initialized")
        }

        onClosed: {
            console.log("Settings panel onClosed called")
            settingsPanel.visible = false
        }
    }

    // Update notification
    UpdateNotification {
        id: updateNotification
        anchors {
            left: parent.left
            right: parent.right
            top: parent.top
        }
        z: 1001 // Above other elements

        onDownloadRequested: {
            updateService.downloadUpdate()
        }

        onSkipRequested: {
            updateService.skipUpdate()
        }

        onRemindLaterRequested: {
            updateService.remindLater()
        }
    }

    // Progress dialog for export/import operations
    ProgressDialog {
        id: progressDialog
        visible: false
    }

    // Connections to handle update service events
    Connections {
        target: updateService

        function onUpdateAvailableChanged() {
            if (updateService.updateAvailable) {
                updateNotification.showNotification(
                    updateService.latestVersion,
                    updateService.releaseNotes
                )
            }
        }

        function onUpdateDownloadStarted() {
            progressDialog.title = "Downloading Update"
            progressDialog.message = "Downloading update..."
            progressDialog.progress = 0
            progressDialog.indeterminate = false
            progressDialog.open()
        }

        function onUpdateDownloadProgress(bytesReceived, bytesTotal) {
            if (bytesTotal > 0) {
                progressDialog.progress = (bytesReceived * 100 / bytesTotal)
            }
        }

        function onUpdateDownloadCompleted(filePath) {
            progressDialog.close()

            // Show success message
            successDialog.title = "Download Complete"
            successDialog.text = "Update downloaded to:\n" + filePath + "\n\nThe installer will open automatically."
            successDialog.open()
        }

        function onUpdateDownloadFailed(error) {
            progressDialog.close()

            // Show error message
            errorDialog.title = "Download Failed"
            errorDialog.text = "Failed to download update: " + error
            errorDialog.open()
        }
    }

    // Connections to handle export/import events
    Connections {
        target: conversationListViewModel

        function onExportStarted(id) {
            progressDialog.title = "Exporting Conversation"
            progressDialog.message = "Exporting conversation..."
            progressDialog.progress = 0
            progressDialog.indeterminate = false
            progressDialog.open()
        }

        function onExportProgress(id, progress) {
            progressDialog.progress = progress
        }

        function onExportCompleted(id, filePath) {
            progressDialog.message = "Export completed successfully!"
            progressDialog.progress = 100

            // Show success message
            successDialog.title = "Export Successful"
            successDialogText.text = "Conversation exported to:\n" + filePath
            successDialog.open()

            progressDialog.close()
        }

        function onExportFailed(id, error) {
            progressDialog.close()

            // Show error message
            errorDialog.title = "Export Failed"
            errorDialogText.text = "Failed to export conversation: " + error
            errorDialog.open()
        }

        function onImportStarted() {
            progressDialog.title = "Importing Conversation"
            progressDialog.message = "Importing conversation..."
            progressDialog.progress = 0
            progressDialog.indeterminate = true
            progressDialog.open()
        }

        function onImportCompleted(id) {
            progressDialog.close()

            // Show success message
            successDialog.title = "Import Successful"
            successDialog.text = "Conversation imported successfully!"
            successDialog.open()
        }

        function onImportFailed(error) {
            progressDialog.close()

            // Show error message
            errorDialog.title = "Import Failed"
            errorDialogText.text = "Failed to import conversation: " + error
            errorDialog.open()
        }
    }

    // Success dialog
    Dialog {
        id: successDialog
        title: "Success"
        anchors.centerIn: parent
        modal: true
        standardButtons: Dialog.Ok

        ColumnLayout {
            anchors.fill: parent
            spacing: 10

            Label {
                id: successDialogText
                text: "Operation completed successfully"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }
        }
    }

    // Error dialog
    Dialog {
        id: errorDialog
        title: "Error"
        anchors.centerIn: parent
        modal: true
        standardButtons: Dialog.Ok

        ColumnLayout {
            anchors.fill: parent
            spacing: 10

            Label {
                id: errorDialogText
                text: "An error occurred"
                Layout.fillWidth: true
                wrapMode: Text.WordWrap
            }
        }
    }

    // Conversation Settings panel (slides in from the right)
    ConversationSettings {
        id: conversationSettingsPanel
        width: Math.min(500, parent.width * 0.8)
        height: parent.height
        anchors.right: parent.right
        visible: false
        z: 1000 // Ensure it's well above other elements

        // Set the current conversation
        conversation: globalChatViewModel.currentConversation

        onClosed: {
            conversationSettingsPanel.visible = false
        }

        onSaved: {
            // Show success message
            successDialog.title = "Settings Saved"
            successDialog.text = "Conversation settings have been saved successfully!"
            successDialog.open()
        }

        // Help section (slides in from the right)
        HelpSection {
            id: helpSection
            width: Math.min(600, parent.width * 0.8)
            height: parent.height
            anchors.right: parent.right
            visible: false
            z: 1000 // Ensure it's well above other elements

            onClosed: {
                helpSection.visible = false
            }
        }

        // MouseArea to handle clicks outside the help section
        MouseArea {
            id: helpSectionOutsideClickArea
            anchors.fill: parent
            visible: helpSection.visible
            z: 999 // Lower z-index than the help section

            onPressed: function(mouse) {
                // Check if the click is within the help section area
                var panelRect = Qt.rect(helpSection.x, 0, helpSection.width, parent.height);
                if (!panelRect.contains(Qt.point(mouse.x, mouse.y))) {
                    // Click is outside the panel, close it
                    helpSection.visible = false;
                    mouse.accepted = true;
                } else {
                    // Click is inside the panel area, don't handle it
                    mouse.accepted = false;
                }
            }
        }
    }

    // New Conversation Dialog
    NewConversationDialog {
        id: newConversationDialog
        anchors.fill: parent
        visible: false
        z: 1000 // Ensure it's well above other elements

        // Set templates
        templates: globalChatViewModel.conversationTemplates

        onClosed: {
            newConversationDialog.visible = false
        }

        onConversationCreated: function(title, provider, model, systemPrompt, temperature, maxTokens) {
            globalChatViewModel.createNewConversation(title, provider, model, systemPrompt, temperature, maxTokens)

            // Show success message
            successDialog.title = "Conversation Created"
            successDialog.text = "New conversation has been created successfully!"
            successDialog.open()
        }
    }

    // MouseArea to handle clicks outside the conversation settings panel
    MouseArea {
        id: conversationSettingsOutsideClickArea
        anchors.fill: parent
        visible: conversationSettingsPanel.visible
        z: 999 // Lower z-index than the settings panel

        onPressed: function(mouse) {
            // Check if the click is within the settings panel area
            var panelRect = Qt.rect(conversationSettingsPanel.x, 0, conversationSettingsPanel.width, parent.height);
            console.log("Conversation Settings Panel Detailed type:", Object.prototype.toString.call(panelRect))
            if (!panelRect.contains(Qt.point(mouse.x, mouse.y))) {
                // Click is outside the panel, close it
                conversationSettingsPanel.visible = false;
                mouse.accepted = true;
            } else {
                // Click is inside the panel area, don't handle it
                mouse.accepted = false;
            }
        }
    }
}