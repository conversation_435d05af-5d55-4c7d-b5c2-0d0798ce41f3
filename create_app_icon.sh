#!/bin/bash

# Create icons directory if it doesn't exist
mkdir -p icons

# Check if Inkscape is installed (for SVG to PNG conversion)
if ! command -v inkscape &> /dev/null; then
    echo "Inkscape is not installed. Please install it to generate icons."
    exit 1
fi

# Check if ImageMagick is installed (for icon generation)
if ! command -v convert &> /dev/null; then
    echo "ImageMagick is not installed. Please install it to generate icons."
    exit 1
fi

# Create a temporary directory for icon generation
TEMP_DIR=$(mktemp -d)
echo "Using temporary directory: $TEMP_DIR"

# Create a simple SVG icon if it doesn't exist
if [ ! -f "icons/app_icon.svg" ]; then
    echo "Creating app icon SVG..."
    cat > icons/app_icon.svg << 'EOL'
<svg xmlns="http://www.w3.org/2000/svg" width="1024" height="1024" viewBox="0 0 1024 1024">
  <rect width="1024" height="1024" rx="200" ry="200" fill="#0d6efd"/>
  <circle cx="512" cy="512" r="350" fill="none" stroke="white" stroke-width="50"/>
  <circle cx="512" cy="512" r="150" fill="white"/>
  <path d="M512 162 L512 862" stroke="white" stroke-width="50" stroke-linecap="round"/>
  <path d="M162 512 L862 512" stroke="white" stroke-width="50" stroke-linecap="round"/>
</svg>
EOL
fi

# Generate PNG files at different sizes
SIZES=(16 32 64 128 256 512 1024)
for SIZE in "${SIZES[@]}"; do
    echo "Generating ${SIZE}x${SIZE} PNG..."
    inkscape -w $SIZE -h $SIZE icons/app_icon.svg -o "$TEMP_DIR/icon_${SIZE}x${SIZE}.png"
    
    # Create @2x version for Retina displays
    if [ $SIZE -lt 512 ]; then
        DOUBLE_SIZE=$((SIZE * 2))
        echo "Generating ${SIZE}x${SIZE}@2x PNG (${DOUBLE_SIZE}x${DOUBLE_SIZE})..."
        inkscape -w $DOUBLE_SIZE -h $DOUBLE_SIZE icons/app_icon.svg -o "$TEMP_DIR/icon_${SIZE}x${SIZE}@2x.png"
    fi
done

# Create .iconset directory
ICONSET_DIR="$TEMP_DIR/AppIcon.iconset"
mkdir -p "$ICONSET_DIR"

# Copy files to .iconset directory with correct names
cp "$TEMP_DIR/icon_16x16.png" "$ICONSET_DIR/icon_16x16.png"
cp "$TEMP_DIR/<EMAIL>" "$ICONSET_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_32x32.png" "$ICONSET_DIR/icon_32x32.png"
cp "$TEMP_DIR/<EMAIL>" "$ICONSET_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_128x128.png" "$ICONSET_DIR/icon_128x128.png"
cp "$TEMP_DIR/<EMAIL>" "$ICONSET_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_256x256.png" "$ICONSET_DIR/icon_256x256.png"
cp "$TEMP_DIR/<EMAIL>" "$ICONSET_DIR/<EMAIL>"
cp "$TEMP_DIR/icon_512x512.png" "$ICONSET_DIR/icon_512x512.png"
cp "$TEMP_DIR/icon_1024x1024.png" "$ICONSET_DIR/<EMAIL>"

# Check if iconutil is available (macOS)
if command -v iconutil &> /dev/null; then
    echo "Generating .icns file using iconutil..."
    iconutil -c icns -o "icons/AppIcon.icns" "$ICONSET_DIR"
else
    echo "iconutil not found. Using ImageMagick to create .icns file..."
    convert "$TEMP_DIR/icon_16x16.png" "$TEMP_DIR/icon_32x32.png" "$TEMP_DIR/icon_64x64.png" \
            "$TEMP_DIR/icon_128x128.png" "$TEMP_DIR/icon_256x256.png" "$TEMP_DIR/icon_512x512.png" \
            "$TEMP_DIR/icon_1024x1024.png" "icons/AppIcon.icns"
fi

# Create Windows .ico file
echo "Generating .ico file for Windows..."
convert "$TEMP_DIR/icon_16x16.png" "$TEMP_DIR/icon_32x32.png" "$TEMP_DIR/icon_64x64.png" \
        "$TEMP_DIR/icon_128x128.png" "$TEMP_DIR/icon_256x256.png" "icons/AppIcon.ico"

# Clean up
echo "Cleaning up temporary files..."
rm -rf "$TEMP_DIR"

echo "Icon generation complete!"
echo "- macOS icon: icons/AppIcon.icns"
echo "- Windows icon: icons/AppIcon.ico"
echo "- SVG source: icons/app_icon.svg"