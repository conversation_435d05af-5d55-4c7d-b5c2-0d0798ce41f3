ColumnLimit: 100

UseTab: Never
IndentWidth: 4
AccessModifierOffset: -4
NamespaceIndentation: Inner

BreakBeforeBraces: Custom
BraceWrapping:
  AfterNamespace: true
  AfterEnum: true
  AfterStruct: true
  AfterClass: true
  SplitEmptyFunction: false
  AfterControlStatement: true
  AfterFunction: true
  AfterUnion: true
  BeforeElse: true


AlwaysBreakTemplateDeclarations: true
BreakConstructorInitializersBeforeComma: true
ConstructorInitializerAllOnOneLineOrOnePerLine: true
AllowShortBlocksOnASingleLine: true
AllowShortFunctionsOnASingleLine: All
AllowShortIfStatementsOnASingleLine: true
AllowShortLoopsOnASingleLine: true

PointerAlignment: Left
AlignConsecutiveAssignments: false
AlignTrailingComments: true

SpaceAfterCStyleCast: true
CommentPragmas: '^ NO-FORMAT:'
