<RCC>
    <qresource prefix="/">
        <file>qml/qmldir</file>
        <file>qml/main.qml</file>
        <file>qml/MainWindow.qml</file>
        <file>qml/components/qmldir</file>
        <file>qml/components/Sidebar.qml</file>
        <file>qml/components/ChatView.qml</file>
        <file>qml/components/InputArea.qml</file>
        <file>qml/components/SettingsPanel.qml</file>
        <file>qml/components/ConversationItem.qml</file>
        <file>qml/components/MessageItem.qml</file>
        <file>qml/components/Button.qml</file>
        <file>qml/components/TextField.qml</file>
        <file>qml/components/MarkdownRenderer.qml</file>
        <file>qml/components/MarkdownText.qml</file>
        <file>qml/components/CodeBlock.qml</file>
        <file>qml/components/ImageViewer.qml</file>
        <file>qml/components/FileAttachment.qml</file>
        <file>qml/components/FilePickerButton.qml</file>
        <file>qml/components/AttachmentPreview.qml</file>
        <file>qml/components/ProgressDialog.qml</file>
        <file>qml/components/ConversationSettings.qml</file>
        <file>qml/components/NewConversationDialog.qml</file>
        <file>qml/components/VoiceRecorder.qml</file>
        <file>qml/components/TextToSpeech.qml</file>
        <file>qml/components/HelpSection.qml</file>
        <file>qml/styles/qmldir</file>
        <file>qml/styles/Colors.qml</file>
        <file>qml/styles/Fonts.qml</file>
    </qresource>
    <qresource prefix="/icons">
        <file>icons/attachment.svg</file>
        <file>icons/image.svg</file>
        <file>icons/pdf.svg</file>
        <file>icons/text.svg</file>
        <file>icons/file.svg</file>
    </qresource>
</RCC>