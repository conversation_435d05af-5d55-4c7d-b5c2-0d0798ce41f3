#include "database_service_test.h"

#include <QStandardPaths>
#include <QDir>
#include <QUuid>
#include <QDateTime>
#include <QJsonDocument>
#include <QJsonObject>

#include "errors/DatabaseErrors.h"

namespace ChamberUI::Tests {

using namespace Errors;

DatabaseServiceTest::DatabaseServiceTest() : m_parentObject() {}

void DatabaseServiceTest::initTestCase() {
    // Create temporary directory for test database
    m_tempDir = std::make_unique<QTemporaryDir>();
    QVERIFY(m_tempDir->isValid());

    // Override the standard paths for testing
    QStandardPaths::setTestModeEnabled(true);
}

void DatabaseServiceTest::cleanupTestCase() {
    m_tempDir.reset();
    QStandardPaths::setTestModeEnabled(false);
}

void DatabaseServiceTest::init() {
    setupTestDatabase();
}

void DatabaseServiceTest::cleanup() {
    cleanupTestDatabase();
}

void DatabaseServiceTest::setupTestDatabase() {
    // Create database service
    gsl::not_null<QObject*> parent = &m_parentObject;
    auto result = DatabaseService::createDatabaseService(&parent);
    QVERIFY(result.has_value());
    m_dbService = std::unique_ptr<DatabaseService>(result.value());
    QVERIFY(m_dbService != nullptr);
}

void DatabaseServiceTest::cleanupTestDatabase() {
    if (m_dbService) {
        m_dbService.reset();
    }
}

void DatabaseServiceTest::testDatabaseCreation() {
    QVERIFY(m_dbService != nullptr);

    // Test that database service was created successfully
    gsl::not_null<QObject*> parent = &m_parentObject;
    auto result = DatabaseService::createDatabaseService(&parent);
    QVERIFY(result.has_value());

    auto dbService = std::unique_ptr<DatabaseService>(result.value());
    QVERIFY(dbService != nullptr);
}

void DatabaseServiceTest::testDatabaseInitialization() {
    // Database should be initialized during construction
    QVERIFY(m_dbService != nullptr);

    // Test that we can perform basic operations
    auto conversations = m_dbService->getAllConversations();
    QVERIFY(conversations.has_value());
    QVERIFY(conversations.value() != nullptr);
}

void DatabaseServiceTest::testTableCreation() {
    // Tables should be created during initialization
    // We can verify this by trying to insert data
    auto conv = createTestConversation();
    auto result = m_dbService->saveConversation(*conv);
    QVERIFY(!result);  // No error should occur
}

void DatabaseServiceTest::testSaveConversation() {
    auto conv = createTestConversation();

    auto result = m_dbService->saveConversation(*conv);
    QVERIFY(!result);  // No error

    // Verify conversation was saved
    auto savedConv = m_dbService->getConversation(conv->id);
    QVERIFY(savedConv.has_value());
    QCOMPARE(savedConv.value()->id, conv->id);
    QCOMPARE(savedConv.value()->title, conv->title);
    QCOMPARE(savedConv.value()->provider, conv->provider);
}

void DatabaseServiceTest::testSaveConversationWithEmptyId() {
    auto conv = createTestConversation("");

    auto result = m_dbService->saveConversation(*conv);
    QVERIFY(result == DatabaseError::DB_TABLE_KEY_LOST);
}

void DatabaseServiceTest::testGetAllConversations() {
    // Save multiple conversations
    auto conv1 = createTestConversation("conv-1");
    auto conv2 = createTestConversation("conv-2");

    m_dbService->saveConversation(*conv1);
    m_dbService->saveConversation(*conv2);

    auto result = m_dbService->getAllConversations();
    QVERIFY(result.has_value());
    QVERIFY(result.value()->size() >= 2);
}

void DatabaseServiceTest::testGetConversation() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto result = m_dbService->getConversation(conv->id);
    QVERIFY(result.has_value());
    QCOMPARE(result.value()->id, conv->id);
}

void DatabaseServiceTest::testGetNonExistentConversation() {
    auto result = m_dbService->getConversation("non-existent-id");
    QVERIFY(!result.has_value());
    QVERIFY(result.error() == DatabaseError::DB_QUERY_FAILED);
}

void DatabaseServiceTest::testDeleteConversation() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto deleteResult = m_dbService->deleteConversation(conv->id);
    QVERIFY(!deleteResult);  // No error

    // Verify conversation was deleted
    auto getResult = m_dbService->getConversation(conv->id);
    QVERIFY(!getResult.has_value());
}

void DatabaseServiceTest::testClearAllConversations() {
    // Save some conversations
    auto conv1 = createTestConversation("conv-1");
    auto conv2 = createTestConversation("conv-2");
    m_dbService->saveConversation(*conv1);
    m_dbService->saveConversation(*conv2);

    auto clearResult = m_dbService->clearAllConversations();
    QVERIFY(!clearResult);  // No error

    // Verify all conversations were deleted
    auto getAllResult = m_dbService->getAllConversations();
    QVERIFY(getAllResult.has_value());
    QCOMPARE(getAllResult.value()->size(), 0);
}

ConversationPtr DatabaseServiceTest::createTestConversation(const QString &id) {
    auto conv = std::make_unique<Conversation>();
    conv->id = id.isEmpty() ? QUuid::createUuid().toString(QUuid::WithoutBraces) : id;
    conv->title = "Test Conversation";
    conv->created_at = QDateTime::currentDateTime();
    conv->updated_at = QDateTime::currentDateTime();
    conv->provider = "openai";
    conv->model = "gpt-4";
    conv->system_prompt = "You are a helpful assistant";
    conv->temperature = 0.7;
    conv->max_tokens = 2000;
    conv->is_template = false;
    conv->template_name = "";
    conv->settings = QVariantMap{{"test_key", "test_value"}};
    return conv;
}

MessagePtr DatabaseServiceTest::createTestMessage(const QString &id, const QString &convId) {
    auto msg = std::make_unique<Message>();
    msg->id = id.isEmpty() ? QUuid::createUuid().toString(QUuid::WithoutBraces) : id;
    msg->conversation_id = convId;
    msg->content = "Test message content";
    msg->role = Message::Role::User;
    msg->timestamp = QDateTime::currentDateTime();
    msg->metadata = QVariantMap{{"test_meta", "meta_value"}};
    return msg;
}

AttachmentPtr DatabaseServiceTest::createTestAttachment(const QString &id, const QString &msgId) {
    auto att = std::make_unique<Attachment>();
    att->id = id.isEmpty() ? QUuid::createUuid().toString(QUuid::WithoutBraces) : id;
    att->message_id = msgId;
    att->filename = "test_file.txt";
    att->path = "/tmp/test_file.txt";
    att->type = Attachment::Type::Text;
    att->mime_type = "text/plain";
    att->size = 1024;
    return att;
}

void DatabaseServiceTest::testSaveMessages() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    QList<MessagePtr> messages;
    messages.append(createTestMessage("msg-1", conv->id));
    messages.append(createTestMessage("msg-2", conv->id));

    auto result = m_dbService->saveMessages(messages);
    QVERIFY(!result);  // No error

    // Verify messages were saved
    auto savedMessages = m_dbService->getConversationMessages(conv->id);
    QVERIFY(savedMessages.has_value());
    QCOMPARE(savedMessages.value()->size(), 2);
}

void DatabaseServiceTest::testSaveEmptyMessages() {
    QList<MessagePtr> emptyMessages;
    auto result = m_dbService->saveMessages(emptyMessages);
    QVERIFY(!result);  // Should handle empty list gracefully
}

void DatabaseServiceTest::testGetConversationMessages() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    QList<MessagePtr> messages;
    messages.append(createTestMessage("msg-1", conv->id));
    messages.append(createTestMessage("msg-2", conv->id));
    m_dbService->saveMessages(messages);

    auto result = m_dbService->getConversationMessages(conv->id);
    QVERIFY(result.has_value());
    QCOMPARE(result.value()->size(), 2);
}

void DatabaseServiceTest::testDeleteMessage() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto msg = createTestMessage("msg-1", conv->id);
    QList<MessagePtr> messages;
    messages.append(std::move(msg));
    m_dbService->saveMessages(messages);

    auto deleteResult = m_dbService->deleteMessage(conv->id, "msg-1");
    QVERIFY(!deleteResult);  // No error

    // Verify message was deleted
    auto getResult = m_dbService->getConversationMessages(conv->id);
    QVERIFY(getResult.has_value());
    QCOMPARE(getResult.value()->size(), 0);
}

void DatabaseServiceTest::testDeleteMessages() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    QList<MessagePtr> messages;
    messages.append(createTestMessage("msg-1", conv->id));
    messages.append(createTestMessage("msg-2", conv->id));
    m_dbService->saveMessages(messages);

    auto deleteResult = m_dbService->deleteMessages(conv->id);
    QVERIFY(!deleteResult);  // No error

    // Verify all messages were deleted
    auto getResult = m_dbService->getConversationMessages(conv->id);
    QVERIFY(getResult.has_value());
    QCOMPARE(getResult.value()->size(), 0);
}

void DatabaseServiceTest::testSaveAttachments() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto msg = createTestMessage("msg-1", conv->id);
    QList<MessagePtr> messages;
    messages.append(std::move(msg));
    m_dbService->saveMessages(messages);

    QList<AttachmentPtr> attachments;
    attachments.append(createTestAttachment("att-1", "msg-1"));
    attachments.append(createTestAttachment("att-2", "msg-1"));

    auto result = m_dbService->saveAttachments(attachments, false);
    QVERIFY(!result);  // No error

    // Verify attachments were saved
    auto savedAttachments = m_dbService->getMessageAttachments("msg-1");
    QVERIFY(savedAttachments.has_value());
    QCOMPARE(savedAttachments.value()->size(), 2);
}

void DatabaseServiceTest::testGetMessageAttachments() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto msg = createTestMessage("msg-1", conv->id);
    QList<MessagePtr> messages;
    messages.append(std::move(msg));
    m_dbService->saveMessages(messages);

    QList<AttachmentPtr> attachments;
    attachments.append(createTestAttachment("att-1", "msg-1"));
    m_dbService->saveAttachments(attachments, false);

    auto result = m_dbService->getMessageAttachments("msg-1");
    QVERIFY(result.has_value());
    QCOMPARE(result.value()->size(), 1);
    QCOMPARE(result.value()->at(0)->filename, "test_file.txt");
}

void DatabaseServiceTest::testDeleteAttachment() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto msg = createTestMessage("msg-1", conv->id);
    QList<MessagePtr> messages;
    messages.append(std::move(msg));
    m_dbService->saveMessages(messages);

    QList<AttachmentPtr> attachments;
    attachments.append(createTestAttachment("att-1", "msg-1"));
    m_dbService->saveAttachments(attachments, false);

    auto deleteResult = m_dbService->deleteAttachment("att-1");
    QVERIFY(!deleteResult);  // No error

    // Verify attachment was deleted
    auto getResult = m_dbService->getMessageAttachments("msg-1");
    QVERIFY(getResult.has_value());
    QCOMPARE(getResult.value()->size(), 0);
}

void DatabaseServiceTest::testDeleteAttachments() {
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    auto msg = createTestMessage("msg-1", conv->id);
    QList<MessagePtr> messages;
    messages.append(std::move(msg));
    m_dbService->saveMessages(messages);

    QList<AttachmentPtr> attachments;
    attachments.append(createTestAttachment("att-1", "msg-1"));
    attachments.append(createTestAttachment("att-2", "msg-1"));
    m_dbService->saveAttachments(attachments, false);

    auto deleteResult = m_dbService->deleteAttachments("msg-1");
    QVERIFY(!deleteResult);  // No error

    // Verify all attachments were deleted
    auto getResult = m_dbService->getMessageAttachments("msg-1");
    QVERIFY(getResult.has_value());
    QCOMPARE(getResult.value()->size(), 0);
}

void DatabaseServiceTest::testSaveSetting() {
    QString key = "test_key";
    QVariant value = "test_value";

    auto result = m_dbService->saveSetting(key, value);
    QVERIFY(!result);  // No error

    // Verify setting was saved
    auto savedValue = m_dbService->getSetting(key);
    QVERIFY(savedValue.has_value());
    QCOMPARE(savedValue.value().toString(), value.toString());
}

void DatabaseServiceTest::testGetSetting() {
    QString key = "test_key";
    QVariant value = QVariantMap{{"nested", "value"}};

    m_dbService->saveSetting(key, value);

    auto result = m_dbService->getSetting(key);
    QVERIFY(result.has_value());
    QCOMPARE(result.value().toMap(), value.toMap());
}

void DatabaseServiceTest::testGetNonExistentSetting() {
    auto result = m_dbService->getSetting("non_existent_key");
    QVERIFY(result.has_value());
    QVERIFY(result.value().isNull());
}

void DatabaseServiceTest::testSaveSettings() {
    QVariantMap settings;
    settings["key1"] = "value1";
    settings["key2"] = 42;
    settings["key3"] = QVariantMap{{"nested", "data"}};

    auto result = m_dbService->saveSettings(settings);
    QVERIFY(!result);  // No error

    // Verify all settings were saved
    auto savedSettings = m_dbService->getSettings();
    QVERIFY(savedSettings.has_value());
    QVERIFY(savedSettings.value()->contains("key1"));
    QVERIFY(savedSettings.value()->contains("key2"));
    QVERIFY(savedSettings.value()->contains("key3"));
}

void DatabaseServiceTest::testGetSettings() {
    // Save some settings first
    m_dbService->saveSetting("key1", "value1");
    m_dbService->saveSetting("key2", 42);

    auto result = m_dbService->getSettings();
    QVERIFY(result.has_value());
    QVERIFY(result.value()->size() >= 2);
    QCOMPARE(result.value()->value("key1").toString(), "value1");
    QCOMPARE(result.value()->value("key2").toInt(), 42);
}

void DatabaseServiceTest::testDeleteSetting() {
    QString key = "test_key";
    m_dbService->saveSetting(key, "test_value");

    auto deleteResult = m_dbService->deleteSetting(key);
    QVERIFY(!deleteResult);  // No error

    // Verify setting was deleted
    auto getResult = m_dbService->getSetting(key);
    QVERIFY(getResult.has_value());
    QVERIFY(getResult.value().isNull());
}

void DatabaseServiceTest::testSaveProviderKey() {
    QString provider = "openai";
    QString key = "test-api-key-12345";

    auto result = m_dbService->saveProviderKey(provider, key);
    QVERIFY(!result);  // No error

    // Verify provider key was saved
    auto savedKey = m_dbService->getProviderKey(provider);
    QVERIFY(savedKey.has_value());
    QCOMPARE(savedKey.value(), key);
}

void DatabaseServiceTest::testGetProviderKey() {
    QString provider = "anthropic";
    QString key = "test-anthropic-key";

    m_dbService->saveProviderKey(provider, key);

    auto result = m_dbService->getProviderKey(provider);
    QVERIFY(result.has_value());
    QCOMPARE(result.value(), key);
}

void DatabaseServiceTest::testGetNonExistentProviderKey() {
    auto result = m_dbService->getProviderKey("non_existent_provider");
    QVERIFY(result.has_value());
    QVERIFY(result.value().isEmpty());
}

void DatabaseServiceTest::testDeleteProviderKey() {
    QString provider = "openai";
    m_dbService->saveProviderKey(provider, "test-key");

    auto deleteResult = m_dbService->deleteProviderKey(provider);
    QVERIFY(!deleteResult);  // No error

    // Verify provider key was deleted
    auto getResult = m_dbService->getProviderKey(provider);
    QVERIFY(getResult.has_value());
    QVERIFY(getResult.value().isEmpty());
}

void DatabaseServiceTest::testGetAllProviderKeys() {
    // Save multiple provider keys
    m_dbService->saveProviderKey("openai", "openai-key");
    m_dbService->saveProviderKey("anthropic", "anthropic-key");

    auto result = m_dbService->getAllProviderKeys();
    QVERIFY(result.has_value());
    QVERIFY(result.value().size() >= 2);

    // Check that providers are present
    bool foundOpenAI = false, foundAnthropic = false;
    for (const auto& provider : result.value()) {
        if (provider->provider == "openai") {
            foundOpenAI = true;
            QCOMPARE(provider->key, "openai-key");
        } else if (provider->provider == "anthropic") {
            foundAnthropic = true;
            QCOMPARE(provider->key, "anthropic-key");
        }
    }
    QVERIFY(foundOpenAI);
    QVERIFY(foundAnthropic);
}

void DatabaseServiceTest::testDatabaseErrorHandling() {
    // Test error handling by trying to get conversation with invalid database state
    // This is a basic test - more sophisticated error testing would require
    // mocking or database corruption simulation

    auto result = m_dbService->getConversation("");
    QVERIFY(!result.has_value());
}

void DatabaseServiceTest::testTransactionHandling() {
    // Test transaction handling with multiple messages
    auto conv = createTestConversation();
    m_dbService->saveConversation(*conv);

    QList<MessagePtr> messages;
    for (int i = 0; i < 5; ++i) {
        messages.append(createTestMessage(QString("msg-%1").arg(i), conv->id));
    }

    auto result = m_dbService->saveMessages(messages);
    QVERIFY(!result);  // No error

    // Verify all messages were saved (transaction succeeded)
    auto savedMessages = m_dbService->getConversationMessages(conv->id);
    QVERIFY(savedMessages.has_value());
    QCOMPARE(savedMessages.value()->size(), 5);
}

} // namespace ChamberUI::Tests

QTEST_MAIN(ChamberUI::Tests::DatabaseServiceTest)
#include "database_service_test.moc"
