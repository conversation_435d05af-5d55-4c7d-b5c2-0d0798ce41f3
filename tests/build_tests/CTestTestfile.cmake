# CMake generated Testfile for 
# Source directory: /Users/<USER>/X/soloholic/ChamberUI2/tests
# Build directory: /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests
# 
# This file includes the relevant testing commands required for 
# testing this directory and lists subdirectories to be tested as well.
add_test(DatabaseServiceTest "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest")
set_tests_properties(DatabaseServiceTest PROPERTIES  ENVIRONMENT "QT_QPA_PLATFORM=offscreen" _BACKTRACE_TRIPLES "/Users/<USER>/X/soloholic/ChamberUI2/tests/CMakeLists.txt;41;add_test;/Users/<USER>/X/soloholic/ChamberUI2/tests/CMakeLists.txt;0;")
