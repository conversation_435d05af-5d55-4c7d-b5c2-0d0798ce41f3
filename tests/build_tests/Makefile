# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target test
test:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running tests..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/ctest --force-new-ctest-process $(ARGS)
.PHONY : test

# Special rule for the target test
test/fast: test
.PHONY : test/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named DatabaseServiceTest

# Build rule for target.
DatabaseServiceTest: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 DatabaseServiceTest
.PHONY : DatabaseServiceTest

# fast build rule for target.
DatabaseServiceTest/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/build
.PHONY : DatabaseServiceTest/fast

#=============================================================================
# Target rules for targets named run_database_tests

# Build rule for target.
run_database_tests: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 run_database_tests
.PHONY : run_database_tests

# fast build rule for target.
run_database_tests/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_database_tests.dir/build.make CMakeFiles/run_database_tests.dir/build
.PHONY : run_database_tests/fast

#=============================================================================
# Target rules for targets named DatabaseServiceTest_autogen_timestamp_deps

# Build rule for target.
DatabaseServiceTest_autogen_timestamp_deps: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 DatabaseServiceTest_autogen_timestamp_deps
.PHONY : DatabaseServiceTest_autogen_timestamp_deps

# fast build rule for target.
DatabaseServiceTest_autogen_timestamp_deps/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build.make CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build
.PHONY : DatabaseServiceTest_autogen_timestamp_deps/fast

#=============================================================================
# Target rules for targets named DatabaseServiceTest_autogen

# Build rule for target.
DatabaseServiceTest_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 DatabaseServiceTest_autogen
.PHONY : DatabaseServiceTest_autogen

# fast build rule for target.
DatabaseServiceTest_autogen/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen.dir/build.make CMakeFiles/DatabaseServiceTest_autogen.dir/build
.PHONY : DatabaseServiceTest_autogen/fast

DatabaseServiceTest_autogen/mocs_compilation.o: DatabaseServiceTest_autogen/mocs_compilation.cpp.o
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.o

# target to build an object file
DatabaseServiceTest_autogen/mocs_compilation.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.cpp.o

DatabaseServiceTest_autogen/mocs_compilation.i: DatabaseServiceTest_autogen/mocs_compilation.cpp.i
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.i

# target to preprocess a source file
DatabaseServiceTest_autogen/mocs_compilation.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.i
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.cpp.i

DatabaseServiceTest_autogen/mocs_compilation.s: DatabaseServiceTest_autogen/mocs_compilation.cpp.s
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.s

# target to generate assembly for a file
DatabaseServiceTest_autogen/mocs_compilation.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.s
.PHONY : DatabaseServiceTest_autogen/mocs_compilation.cpp.s

database_service_test.o: database_service_test.cpp.o
.PHONY : database_service_test.o

# target to build an object file
database_service_test.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o
.PHONY : database_service_test.cpp.o

database_service_test.i: database_service_test.cpp.i
.PHONY : database_service_test.i

# target to preprocess a source file
database_service_test.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.i
.PHONY : database_service_test.cpp.i

database_service_test.s: database_service_test.cpp.s
.PHONY : database_service_test.s

# target to generate assembly for a file
database_service_test.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.s
.PHONY : database_service_test.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... test"
	@echo "... DatabaseServiceTest_autogen"
	@echo "... DatabaseServiceTest_autogen_timestamp_deps"
	@echo "... run_database_tests"
	@echo "... DatabaseServiceTest"
	@echo "... DatabaseServiceTest_autogen/mocs_compilation.o"
	@echo "... DatabaseServiceTest_autogen/mocs_compilation.i"
	@echo "... DatabaseServiceTest_autogen/mocs_compilation.s"
	@echo "... database_service_test.o"
	@echo "... database_service_test.i"
	@echo "... database_service_test.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

