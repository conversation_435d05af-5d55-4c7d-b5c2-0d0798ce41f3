# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests

# Utility rule file for DatabaseServiceTest_autogen.

# Include any custom commands dependencies for this target.
include CMakeFiles/DatabaseServiceTest_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DatabaseServiceTest_autogen.dir/progress.make

CMakeFiles/DatabaseServiceTest_autogen: DatabaseServiceTest_autogen/timestamp

DatabaseServiceTest_autogen/timestamp: /opt/homebrew/share/qt/libexec/moc
DatabaseServiceTest_autogen/timestamp: CMakeFiles/DatabaseServiceTest_autogen.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target DatabaseServiceTest"
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E cmake_autogen /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest_autogen.dir/AutogenInfo.json Debug
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E touch /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/timestamp

CMakeFiles/DatabaseServiceTest_autogen.dir/codegen:
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/codegen

DatabaseServiceTest_autogen: CMakeFiles/DatabaseServiceTest_autogen
DatabaseServiceTest_autogen: DatabaseServiceTest_autogen/timestamp
DatabaseServiceTest_autogen: CMakeFiles/DatabaseServiceTest_autogen.dir/build.make
.PHONY : DatabaseServiceTest_autogen

# Rule to build all files generated by this target.
CMakeFiles/DatabaseServiceTest_autogen.dir/build: DatabaseServiceTest_autogen
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/build

CMakeFiles/DatabaseServiceTest_autogen.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DatabaseServiceTest_autogen.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/clean

CMakeFiles/DatabaseServiceTest_autogen.dir/depend:
	cd /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/X/soloholic/ChamberUI2/tests /Users/<USER>/X/soloholic/ChamberUI2/tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/depend

