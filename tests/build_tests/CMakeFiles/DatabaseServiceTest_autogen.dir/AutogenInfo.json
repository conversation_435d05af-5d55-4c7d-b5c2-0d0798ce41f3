{"BUILD_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen", "CMAKE_BINARY_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests", "CMAKE_CURRENT_BINARY_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests", "CMAKE_CURRENT_SOURCE_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests", "CMAKE_EXECUTABLE": "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake", "CMAKE_LIST_FILES": ["/Users/<USER>/X/soloholic/ChamberUI2/tests/CMakeLists.txt", "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/3.31.4/CMakeSystem.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeSystemSpecificInitialize.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Darwin-Initialize.cmake", "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/3.31.4/CMakeCCompiler.cmake", "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/3.31.4/CMakeCXXCompiler.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeSystemSpecificInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeGenericSystem.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeInitializeConfigs.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Darwin.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/UnixPaths.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/AppleClang-C.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/Clang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/GNU.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/CMakeCommonCompilerMacros.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-AppleClang-C.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-Clang-C.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CMakeCLinkerInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Linker/AppleClang-C.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Linker/AppleClang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-C.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCXXInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeLanguageInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/AppleClang-CXX.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Compiler/Clang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-AppleClang-CXX.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-Clang-CXX.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Apple-Clang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCommonLanguageInclude.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CMakeCXXLinkerInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CMakeCommonLinkerInformation.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Linker/AppleClang-CXX.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Linker/AppleClang.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang-CXX.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Platform/Linker/Apple-AppleClang.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6ConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6ConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6Config.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6ConfigExtras.cmake", "/opt/homebrew/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/homebrew/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/homebrew/lib/cmake/Qt6/QtInstallPaths.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6Targets.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6VersionlessAliasTargets.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeature.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckCXXCompilerFlag.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CheckCompilerFlag.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CheckFlagCommonConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCheckCompilerFlagCommonPatterns.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtFeatureCommon.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAndroidHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicAppleHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicCMakeVersionHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicDependencyHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicExternalProjectHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFinalizerHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicFindPackageHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicGitHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicPluginHelpers_v2.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomAttributionHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomCpeHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomDepHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomFileHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomGenerationHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomLicenseHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomOpsHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPurlHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomPythonHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomQtEntityHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicSbomSystemDepHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTargetHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicTestHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicToolHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWalkLibsHelpers.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/QtPublicWindowsHelpers.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6/Qt6Dependencies.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/FindThreads.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckLibraryExists.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckIncludeFile.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckCSourceCompiles.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/Internal/CheckSourceCompiles.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/FindPackageMessage.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreDependencies.cmake", "/opt/homebrew/Cellar/qt/6.9.0/lib/cmake/Qt6/FindWrapAtomic.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CheckCXXSourceCompiles.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/FindPackageHandleStandardArgs.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/FindPackageMessage.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6CoreTools/Qt6CoreToolsVersionlessTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateTargets.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6CorePrivate/Qt6CorePrivateVersionlessAliasTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreMacros.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreConfigExtras.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/GNUInstallDirs.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CorePlugins.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinBluetoothPermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinBluetoothPermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinBluetoothPermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinBluetoothPermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinBluetoothPermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCalendarPermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCalendarPermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCalendarPermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCalendarPermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCalendarPermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCameraPermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCameraPermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCameraPermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCameraPermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinCameraPermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinContactsPermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinContactsPermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinContactsPermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinContactsPermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinContactsPermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinLocationPermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinLocationPermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinLocationPermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinLocationPermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinLocationPermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinMicrophonePermissionPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinMicrophonePermissionPluginDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinMicrophonePermissionPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinMicrophonePermissionPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6QDarwinMicrophonePermissionPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Core/Qt6CoreVersionlessAliasTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateTargets.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6TestPrivate/Qt6TestPrivateVersionlessAliasTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Test/QtTestProperties.cmake", "/opt/homebrew/lib/cmake/Qt6Test/Qt6TestVersionlessAliasTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateConfigVersion.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateConfigVersionImpl.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateDependencies.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateTargets.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6SqlPrivate/Qt6SqlPrivateVersionlessAliasTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlPlugins.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6QSQLiteDriverPluginConfig.cmake", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeFindDependencyMacro.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6QSQLiteDriverPluginTargets.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6QSQLiteDriverPluginTargets-release.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6QSQLiteDriverPluginAdditionalTargetInfo.cmake", "/opt/homebrew/lib/cmake/Qt6Sql/Qt6SqlVersionlessAliasTargets.cmake"], "CMAKE_SOURCE_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests", "CROSS_CONFIG": false, "DEP_FILE": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/deps", "DEP_FILE_RULE_NAME": "DatabaseServiceTest_autogen/timestamp", "HEADERS": [["/Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.h", "Mu", "EWIEGA46WW/moc_database_service_test.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/include", "MOC_COMPILATION_FILE": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_SQL_LIB", "QT_TESTCASE_BUILDDIR=\"/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests\"", "QT_TESTCASE_SOURCEDIR=\"/Users/<USER>/X/soloholic/ChamberUI2/tests\"", "QT_TESTLIB_LIB"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/Users/<USER>/X/soloholic/ChamberUI2/tests/src", "/opt/homebrew/lib/QtTest.framework/Headers", "/opt/homebrew/lib/QtTest.framework", "/opt/homebrew/lib/QtCore.framework/Headers", "/opt/homebrew/lib/QtCore.framework", "/opt/homebrew/share/qt/mkspecs/macx-clang", "/opt/homebrew/include", "/opt/homebrew/lib/QtSql.framework/Headers", "/opt/homebrew/lib/QtSql.framework", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include/c++/v1", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/clang/17/include", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk/usr/include", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT", "Q_GADGET_EXPORT", "Q_ENUM_NS"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++", "-std=gnu++2b", "-dM", "-E", "-c", "/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 16, "PARSE_CACHE_FILE": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/opt/homebrew/share/qt/libexec/moc", "QT_UIC_EXECUTABLE": "", "QT_VERSION_MAJOR": 6, "QT_VERSION_MINOR": 9, "SETTINGS_FILE": "/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest_autogen.dir/AutogenUsed.txt", "SOURCES": [["/Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.cpp", "Mu", null]], "USE_BETTER_GRAPH": true, "VERBOSITY": 0}