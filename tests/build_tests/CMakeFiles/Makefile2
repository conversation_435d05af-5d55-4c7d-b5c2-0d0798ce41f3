# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/DatabaseServiceTest.dir/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/DatabaseServiceTest.dir/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall:
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/DatabaseServiceTest.dir/clean
clean: CMakeFiles/run_database_tests.dir/clean
clean: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/clean
clean: CMakeFiles/DatabaseServiceTest_autogen.dir/clean
.PHONY : clean

#=============================================================================
# Target rules for target CMakeFiles/DatabaseServiceTest.dir

# All Build rule for target.
CMakeFiles/DatabaseServiceTest.dir/all: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all
CMakeFiles/DatabaseServiceTest.dir/all: CMakeFiles/DatabaseServiceTest_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=1,2,3,4 "Built target DatabaseServiceTest"
.PHONY : CMakeFiles/DatabaseServiceTest.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DatabaseServiceTest.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DatabaseServiceTest.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
.PHONY : CMakeFiles/DatabaseServiceTest.dir/rule

# Convenience name for target.
DatabaseServiceTest: CMakeFiles/DatabaseServiceTest.dir/rule
.PHONY : DatabaseServiceTest

# codegen rule for target.
CMakeFiles/DatabaseServiceTest.dir/codegen: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=1,2,3,4 "Finished codegen for target DatabaseServiceTest"
.PHONY : CMakeFiles/DatabaseServiceTest.dir/codegen

# clean rule for target.
CMakeFiles/DatabaseServiceTest.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest.dir/build.make CMakeFiles/DatabaseServiceTest.dir/clean
.PHONY : CMakeFiles/DatabaseServiceTest.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/run_database_tests.dir

# All Build rule for target.
CMakeFiles/run_database_tests.dir/all: CMakeFiles/DatabaseServiceTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_database_tests.dir/build.make CMakeFiles/run_database_tests.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_database_tests.dir/build.make CMakeFiles/run_database_tests.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=6 "Built target run_database_tests"
.PHONY : CMakeFiles/run_database_tests.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/run_database_tests.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/run_database_tests.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
.PHONY : CMakeFiles/run_database_tests.dir/rule

# Convenience name for target.
run_database_tests: CMakeFiles/run_database_tests.dir/rule
.PHONY : run_database_tests

# codegen rule for target.
CMakeFiles/run_database_tests.dir/codegen: CMakeFiles/DatabaseServiceTest.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_database_tests.dir/build.make CMakeFiles/run_database_tests.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=6 "Finished codegen for target run_database_tests"
.PHONY : CMakeFiles/run_database_tests.dir/codegen

# clean rule for target.
CMakeFiles/run_database_tests.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/run_database_tests.dir/build.make CMakeFiles/run_database_tests.dir/clean
.PHONY : CMakeFiles/run_database_tests.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir

# All Build rule for target.
CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build.make CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build.make CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num= "Built target DatabaseServiceTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
.PHONY : CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/rule

# Convenience name for target.
DatabaseServiceTest_autogen_timestamp_deps: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/rule
.PHONY : DatabaseServiceTest_autogen_timestamp_deps

# codegen rule for target.
CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build.make CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num= "Finished codegen for target DatabaseServiceTest_autogen_timestamp_deps"
.PHONY : CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/codegen

# clean rule for target.
CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/build.make CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/clean
.PHONY : CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/clean

#=============================================================================
# Target rules for target CMakeFiles/DatabaseServiceTest_autogen.dir

# All Build rule for target.
CMakeFiles/DatabaseServiceTest_autogen.dir/all: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen.dir/build.make CMakeFiles/DatabaseServiceTest_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen.dir/build.make CMakeFiles/DatabaseServiceTest_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=5 "Built target DatabaseServiceTest_autogen"
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/DatabaseServiceTest_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 1
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/DatabaseServiceTest_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles 0
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/rule

# Convenience name for target.
DatabaseServiceTest_autogen: CMakeFiles/DatabaseServiceTest_autogen.dir/rule
.PHONY : DatabaseServiceTest_autogen

# codegen rule for target.
CMakeFiles/DatabaseServiceTest_autogen.dir/codegen: CMakeFiles/DatabaseServiceTest_autogen_timestamp_deps.dir/all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen.dir/build.make CMakeFiles/DatabaseServiceTest_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=5 "Finished codegen for target DatabaseServiceTest_autogen"
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/codegen

# clean rule for target.
CMakeFiles/DatabaseServiceTest_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/DatabaseServiceTest_autogen.dir/build.make CMakeFiles/DatabaseServiceTest_autogen.dir/clean
.PHONY : CMakeFiles/DatabaseServiceTest_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

