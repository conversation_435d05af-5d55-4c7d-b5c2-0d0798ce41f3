# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# compile CXX with /Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++
CXX_DEFINES = -DQT_CORE_LIB -DQT_SQL_LIB -DQT_TESTCASE_BUILDDIR=\"/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests\" -DQT_TESTCASE_SOURCEDIR=\"/Users/<USER>/X/soloholic/ChamberUI2/tests\" -DQT_TESTLIB_LIB

CXX_INCLUDES = -I/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/include -I/Users/<USER>/X/soloholic/ChamberUI2/tests/src -isystem /opt/homebrew/lib/QtTest.framework/Headers -iframework /opt/homebrew/lib -isystem /opt/homebrew/lib/QtCore.framework/Headers -isystem /opt/homebrew/share/qt/mkspecs/macx-clang -isystem /opt/homebrew/include -isystem /opt/homebrew/lib/QtSql.framework/Headers

CXX_FLAGSarm64 = -g -std=gnu++2b -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk

CXX_FLAGS = -g -std=gnu++2b -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk

