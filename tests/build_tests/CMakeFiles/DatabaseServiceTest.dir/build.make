# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake

# The command to remove a file.
RM = /Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests

# Include any dependencies generated for this target.
include CMakeFiles/DatabaseServiceTest.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/DatabaseServiceTest.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/DatabaseServiceTest.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/DatabaseServiceTest.dir/flags.make

DatabaseServiceTest_autogen/timestamp: /opt/homebrew/share/qt/libexec/moc
DatabaseServiceTest_autogen/timestamp: CMakeFiles/DatabaseServiceTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC for target DatabaseServiceTest"
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E cmake_autogen /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest_autogen.dir/AutogenInfo.json Debug
	/Users/<USER>/.bin/miniconda3/lib/python3.12/site-packages/cmake/data/bin/cmake -E touch /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/timestamp

CMakeFiles/DatabaseServiceTest.dir/codegen:
.PHONY : CMakeFiles/DatabaseServiceTest.dir/codegen

CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o: CMakeFiles/DatabaseServiceTest.dir/flags.make
CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o: DatabaseServiceTest_autogen/mocs_compilation.cpp
CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o: CMakeFiles/DatabaseServiceTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o -MF CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/mocs_compilation.cpp

CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/mocs_compilation.cpp > CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.i

CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/DatabaseServiceTest_autogen/mocs_compilation.cpp -o CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.s

CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o: CMakeFiles/DatabaseServiceTest.dir/flags.make
CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o: /Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.cpp
CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o: CMakeFiles/DatabaseServiceTest.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o -MF CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o.d -o CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o -c /Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.cpp

CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.i"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.cpp > CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.i

CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.s"
	/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/X/soloholic/ChamberUI2/tests/database_service_test.cpp -o CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.s

# Object files for target DatabaseServiceTest
DatabaseServiceTest_OBJECTS = \
"CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o"

# External object files for target DatabaseServiceTest
DatabaseServiceTest_EXTERNAL_OBJECTS =

DatabaseServiceTest: CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o
DatabaseServiceTest: CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o
DatabaseServiceTest: CMakeFiles/DatabaseServiceTest.dir/build.make
DatabaseServiceTest: /opt/homebrew/lib/QtTest.framework/Versions/A/QtTest
DatabaseServiceTest: /opt/homebrew/lib/QtSql.framework/Versions/A/QtSql
DatabaseServiceTest: /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore
DatabaseServiceTest: CMakeFiles/DatabaseServiceTest.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable DatabaseServiceTest"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/DatabaseServiceTest.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/DatabaseServiceTest.dir/build: DatabaseServiceTest
.PHONY : CMakeFiles/DatabaseServiceTest.dir/build

CMakeFiles/DatabaseServiceTest.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/DatabaseServiceTest.dir/cmake_clean.cmake
.PHONY : CMakeFiles/DatabaseServiceTest.dir/clean

CMakeFiles/DatabaseServiceTest.dir/depend: DatabaseServiceTest_autogen/timestamp
	cd /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/X/soloholic/ChamberUI2/tests /Users/<USER>/X/soloholic/ChamberUI2/tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests /Users/<USER>/X/soloholic/ChamberUI2/tests/build_tests/CMakeFiles/DatabaseServiceTest.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/DatabaseServiceTest.dir/depend

