/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/c++ -g -arch arm64 -isysroot /Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.4.sdk -Wl,-search_paths_first -Wl,-headerpad_max_install_names CMakeFiles/DatabaseServiceTest.dir/DatabaseServiceTest_autogen/mocs_compilation.cpp.o CMakeFiles/DatabaseServiceTest.dir/database_service_test.cpp.o -o DatabaseServiceTest -F/opt/homebrew/lib  -Wl,-rpath,/opt/homebrew/lib /opt/homebrew/lib/QtTest.framework/Versions/A/QtTest /opt/homebrew/lib/QtSql.framework/Versions/A/QtSql -lChamberServices -lChamberModels -lChamberErrors -lMicrosoft.GSL::GSL -lmagic_enum::magic_enum -framework Security -framework AppKit -framework ApplicationServices -framework Foundation /opt/homebrew/lib/QtCore.framework/Versions/A/QtCore -framework IOKit -framework DiskArbitration -framework UniformTypeIdentifiers
