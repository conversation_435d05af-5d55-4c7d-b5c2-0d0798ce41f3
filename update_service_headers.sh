#!/bin/bash

# 服务类头文件列表
SERVICE_HEADERS=(
    "src/services/conversationmanager.h"
    "src/services/cacheservice.h"
    "src/services/providermanager.h"
    "src/services/provider.h"
    "src/services/openai_provider.h"
    "src/services/anthropic_provider.h"
    "src/services/exportservice.h"
    "src/services/importservice.h"
    "src/services/voiceservice.h"
    "src/services/updateservice.h"
    "src/services/messageprocessor.h"
)

# 为每个头文件添加导出宏
for header in "${SERVICE_HEADERS[@]}"; do
    echo "Processing $header..."
    
    # 检查文件是否存在
    if [ ! -f "$header" ]; then
        echo "File $header does not exist, skipping."
        continue
    fi
    
    # 添加 chamberservices_global.h 包含
    sed -i '' '1,10s/#include <Q/#include "chamberservices_global.h"\n\n#include <Q/' "$header"
    
    # 添加导出宏到类定义
    sed -i '' 's/class \([A-Za-z0-9_]*\) : public QObject/class CHAMBERSERVICES_EXPORT \1 : public QObject/' "$header"
    
    echo "Updated $header"
done

echo "All service headers updated."
